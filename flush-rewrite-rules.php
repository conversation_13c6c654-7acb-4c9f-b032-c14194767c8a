<?php
/**
 * <PERSON><PERSON><PERSON> to flush WordPress rewrite rules and ensure custom post type URLs work
 */

// Set up WordPress environment
define('WP_USE_THEMES', false);
require_once('wp-load.php');

// Register custom post types (needed before flushing)
function temp_register_pool_products() {
    register_post_type('pool_product', array(
        'labels' => array(
            'name' => 'Pool Products',
            'singular_name' => 'Pool Product',
        ),
        'public' => true,
        'has_archive' => true,
        'supports' => array('title', 'editor', 'thumbnail', 'excerpt'),
        'menu_icon' => 'dashicons-admin-home',
        'rewrite' => array('slug' => 'pools'),
    ));
}

// Register the post type
temp_register_pool_products();

// Flush rewrite rules
flush_rewrite_rules();

echo "✅ Rewrite rules flushed successfully!\n";
echo "📍 Pool products should now be accessible at: " . home_url('/pools/') . "\n";
echo "🔗 Try visiting: http://localhost:8080/pools/\n";
?>
