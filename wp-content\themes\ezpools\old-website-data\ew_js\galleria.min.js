/**
 * Galleria - v1.6.1 2019-10-17
 * 
 *
 * Copyright (c) 2010 - 2019 worse is better UG
 * Licensed under the MIT License.
 */


!function(a,b){"function"==typeof define&&define.amd?define(["jquery"],function(c){return b(a,c)}):"object"==typeof module&&module.exports?module.exports=b(a,require("jquery")):a.Galleria=b(a,a.jQuery)}(window,function(a,b,c,d){var e=a.document,f=b(e),g=b(a),h=b,i=Array.prototype,j=!0,k=3e4,l=!1,m=navigator.userAgent.toLowerCase(),n=a.location.hash.replace(/#\//,""),o="file:"==a.location.protocol?"http:":a.location.protocol,p=Math,q=function(){},r=function(){return!1},s=!(a.screen.width>1279&&1==a.devicePixelRatio||a.screen.width>1e3&&a.innerWidth<.9*a.screen.width),t=function(){var a=3,b=e.createElement("div"),c=b.getElementsByTagName("i");do{b.innerHTML="\x3c!--[if gt IE "+ ++a+"]><i></i><![endif]--\x3e"}while(c[0]);return a>4?a:e.documentMode||d}(),u=function(){return{html:e.documentElement,body:e.body,head:e.getElementsByTagName("head")[0],title:e.title}},v=a.parent!==a.self,w="data ready thumbnail loadstart loadfinish image play pause progress fullscreen_enter fullscreen_exit idle_enter idle_exit rescale lightbox_open lightbox_close lightbox_image",x=function(){var a=[];return b.each(w.split(" "),function(b,c){a.push(c),/_/.test(c)&&a.push(c.replace(/_/g,""))}),a}(),y=function(a){var c;return"object"!=typeof a?a:(b.each(a,function(d,e){/^[a-z]+_/.test(d)&&(c="",b.each(d.split("_"),function(a,b){c+=a>0?b.substr(0,1).toUpperCase()+b.substr(1):b}),a[c]=e,delete a[d])}),a)},z=function(a){return b.inArray(a,x)>-1?c[a.toUpperCase()]:a},A={youtube:{reg:/https?:\/\/(?:[a-zA_Z]{2,3}.)?(?:youtube\.com\/watch\?)((?:[\w\d\-\_\=]+&amp;(?:amp;)?)*v(?:&lt;[A-Z]+&gt;)?=([0-9a-zA-Z\-\_]+))/i,embed:function(){return o+"//www.youtube.com/embed/"+this.id},get_thumb:function(a){return o+"//img.youtube.com/vi/"+this.id+"/default.jpg"},get_image:function(a){return o+"//img.youtube.com/vi/"+this.id+"/maxresdefault.jpg"}},vimeo:{reg:/https?:\/\/(?:www\.)?(vimeo\.com)\/(?:hd#)?([0-9]+)/i,embed:function(){return o+"//player.vimeo.com/video/"+this.id},getUrl:function(){return o+"//vimeo.com/api/v2/video/"+this.id+".json?callback=?"},get_thumb:function(a){return a[0].thumbnail_medium},get_image:function(a){return a[0].thumbnail_large}},dailymotion:{reg:/https?:\/\/(?:www\.)?(dailymotion\.com)\/video\/([^_]+)/,embed:function(){return o+"//www.dailymotion.com/embed/video/"+this.id},getUrl:function(){return"https://api.dailymotion.com/video/"+this.id+"?fields=thumbnail_240_url,thumbnail_720_url&callback=?"},get_thumb:function(a){return a.thumbnail_240_url},get_image:function(a){return a.thumbnail_720_url}},_inst:[]},B=function(c,d){for(var e=0;e<A._inst.length;e++)if(A._inst[e].id===d&&A._inst[e].type==c)return A._inst[e];this.type=c,this.id=d,this.readys=[],A._inst.push(this);var f=this;b.extend(this,A[c]),_videoThumbs=function(a){f.data=a,b.each(f.readys,function(a,b){b(f.data)}),f.readys=[]},this.hasOwnProperty("getUrl")?b.getJSON(this.getUrl(),_videoThumbs):a.setTimeout(_videoThumbs,400),this.getMedia=function(a,b,c){c=c||q;var d=this,e=function(c){b(d["get_"+a](c))};try{d.data?e(d.data):d.readys.push(e)}catch(a){c()}}},C=function(a){var b;for(var c in A)if((b=a&&A[c].reg&&a.match(A[c].reg))&&b.length)return{id:b[2],provider:c};return!1},D={support:function(){var a=u().html;return!v&&(a.requestFullscreen||a.msRequestFullscreen||a.mozRequestFullScreen||a.webkitRequestFullScreen)}(),callback:q,enter:function(a,b,c){this.instance=a,this.callback=b||q,c=c||u().html,c.requestFullscreen?c.requestFullscreen():c.msRequestFullscreen?c.msRequestFullscreen():c.mozRequestFullScreen?c.mozRequestFullScreen():c.webkitRequestFullScreen&&c.webkitRequestFullScreen()},exit:function(a){this.callback=a||q,e.exitFullscreen?e.exitFullscreen():e.msExitFullscreen?e.msExitFullscreen():e.mozCancelFullScreen?e.mozCancelFullScreen():e.webkitCancelFullScreen&&e.webkitCancelFullScreen()},instance:null,listen:function(){if(this.support){var a=function(){if(D.instance){var a=D.instance._fullscreen;e.fullscreen||e.mozFullScreen||e.webkitIsFullScreen||e.msFullscreenElement&&null!==e.msFullscreenElement?a._enter(D.callback):a._exit(D.callback)}};e.addEventListener("fullscreenchange",a,!1),e.addEventListener("MSFullscreenChange",a,!1),e.addEventListener("mozfullscreenchange",a,!1),e.addEventListener("webkitfullscreenchange",a,!1)}}},E=[],F=[],G=!1,H=!1,I=[],J=[],K=function(a){J.push(a),b.each(I,function(b,c){c._options.theme!=a.name&&(c._initialized||c._options.theme)||(c.theme=a,c._init.call(c))})},L=function(){return{clearTimer:function(a){b.each(c.get(),function(){this.clearTimer(a)})},addTimer:function(a){b.each(c.get(),function(){this.addTimer(a)})},array:function(a){return i.slice.call(a,0)},create:function(a,b){b=b||"div";var c=e.createElement(b);return c.className=a,c},removeFromArray:function(a,c){return b.each(a,function(b,d){if(d==c)return a.splice(b,1),!1}),a},getScriptPath:function(a){a=a||b("script:last").attr("src");var c=a.split("/");return 1==c.length?"":(c.pop(),c.join("/")+"/")},animate:function(){var d,f,g,h,i,j,k,l=function(b){var c,d="transition WebkitTransition MozTransition OTransition".split(" ");if(a.opera)return!1;for(c=0;d[c];c++)if(void 0!==b[d[c]])return d[c];return!1}((e.body||e.documentElement).style),m={MozTransition:"transitionend",OTransition:"oTransitionEnd",WebkitTransition:"webkitTransitionEnd",transition:"transitionend"}[l],n={_default:[.25,.1,.25,1],galleria:[.645,.045,.355,1],galleriaIn:[.55,.085,.68,.53],galleriaOut:[.25,.46,.45,.94],ease:[.25,0,.25,1],linear:[.25,.25,.75,.75],"ease-in":[.42,0,1,1],"ease-out":[0,0,.58,1],"ease-in-out":[.42,0,.58,1]},o=function(a,c,d){var e={};d=d||"transition",b.each("webkit moz ms o".split(" "),function(){e["-"+this+"-"+d]=c}),a.css(e)},p=function(a){o(a,"none","transition"),c.WEBKIT&&c.TOUCH&&(o(a,"translate3d(0,0,0)","transform"),a.data("revert")&&(a.css(a.data("revert")),a.data("revert",null)))};return function(e,r,s){return s=b.extend({duration:400,complete:q,stop:!1},s),e=b(e),s.duration?l?(s.stop&&(e.off(m),p(e)),d=!1,b.each(r,function(a,b){k=e.css(a),L.parseValue(k)!=L.parseValue(b)&&(d=!0),e.css(a,k)}),d?(f=[],g=s.easing in n?n[s.easing]:n._default,h=" "+s.duration+"ms cubic-bezier("+g.join(",")+")",void a.setTimeout(function(a,d,e,g){return function(){a.one(d,function(a){return function(){p(a),s.complete.call(a[0])}}(a)),c.WEBKIT&&c.TOUCH&&(i={},j=[0,0,0],b.each(["left","top"],function(b,c){c in e&&(j[b]=L.parseValue(e[c])-L.parseValue(a.css(c))+"px",i[c]=e[c],delete e[c])}),(j[0]||j[1])&&(a.data("revert",i),f.push("-webkit-transform"+g),o(a,"translate3d("+j.join(",")+")","transform"))),b.each(e,function(a,b){f.push(a+g)}),o(a,f.join(",")),a.css(e)}}(e,m,r,h),2)):void a.setTimeout(function(){s.complete.call(e[0])},s.duration)):void e.animate(r,s):(e.css(r),void s.complete.call(e[0]))}}(),removeAlpha:function(a){if(a instanceof h&&(a=a[0]),t<9&&a){var b=a.style,c=a.currentStyle,d=c&&c.filter||b.filter||"";/alpha/.test(d)&&(b.filter=d.replace(/alpha\([^)]*\)/i,""))}},forceStyles:function(a,c){a=b(a),a.attr("style")&&a.data("styles",a.attr("style")).removeAttr("style"),a.css(c)},revertStyles:function(){b.each(L.array(arguments),function(a,c){c=b(c),c.removeAttr("style"),c.attr("style",""),c.data("styles")&&c.attr("style",c.data("styles")).data("styles",null)})},moveOut:function(a){L.forceStyles(a,{position:"absolute",left:-1e4})},moveIn:function(){L.revertStyles.apply(L,L.array(arguments))},hide:function(a,c,d){d=d||q;var e=b(a);a=e[0],e.data("opacity")||e.data("opacity",e.css("opacity"));var f={opacity:0};if(c){var g=t<9&&a?function(){L.removeAlpha(a),a.style.visibility="hidden",d.call(a)}:d;L.animate(a,f,{duration:c,complete:g,stop:!0})}else t<9&&a?(L.removeAlpha(a),a.style.visibility="hidden"):e.css(f)},show:function(a,c,d){d=d||q;var e=b(a);a=e[0];var f=parseFloat(e.data("opacity"))||1,g={opacity:f};if(c){t<9&&(e.css("opacity",0),a.style.visibility="visible");var h=t<9&&a?function(){1==g.opacity&&L.removeAlpha(a),d.call(a)}:d;L.animate(a,g,{duration:c,complete:h,stop:!0})}else t<9&&1==g.opacity&&a?(L.removeAlpha(a),a.style.visibility="visible"):e.css(g)},wait:function(d){c._waiters=c._waiters||[],d=b.extend({until:r,success:q,error:function(){c.raise("Could not complete wait function.")},timeout:3e3},d);var e,f,g,h=L.timestamp(),i=function(){return f=L.timestamp(),e=f-h,L.removeFromArray(c._waiters,g),d.until(e)?(d.success(),!1):"number"==typeof d.timeout&&f>=h+d.timeout?(d.error(),!1):void c._waiters.push(g=a.setTimeout(i,10))};c._waiters.push(g=a.setTimeout(i,10))},toggleQuality:function(a,b){7!==t&&8!==t||!a||"IMG"!=a.nodeName.toUpperCase()||(void 0===b&&(b="nearest-neighbor"===a.style.msInterpolationMode),a.style.msInterpolationMode=b?"bicubic":"nearest-neighbor")},insertStyleTag:function(a,c){if(!c||!b("#"+c).length){var d=e.createElement("style");if(c&&(d.id=c),u().head.appendChild(d),d.styleSheet)d.styleSheet.cssText=a;else{var f=e.createTextNode(a);d.appendChild(f)}}},loadScript:function(a,c){var d=!1,e=b("<script>").attr({src:a,async:!0}).get(0);e.onload=e.onreadystatechange=function(){d||this.readyState&&"loaded"!==this.readyState&&"complete"!==this.readyState||(d=!0,e.onload=e.onreadystatechange=null,"function"==typeof c&&c.call(this,this))},u().head.appendChild(e)},parseValue:function(a){if("number"==typeof a)return a;if("string"==typeof a){var b=a.match(/\-?\d|\./g);return b&&b.constructor===Array?1*b.join(""):0}return 0},timestamp:function(){return(new Date).getTime()},loadCSS:function(a,f,g){var h,i;if(b("link[rel=stylesheet]").each(function(){if(new RegExp(a).test(this.href))return h=this,!1}),"function"==typeof f&&(g=f,f=d),g=g||q,h)return g.call(h,h),h;if(i=e.styleSheets.length,b("#"+f).length)b("#"+f).attr("href",a),i--;else{h=b("<link>").attr({rel:"stylesheet",href:a,id:f}).get(0);var j=b('link[rel="stylesheet"], style');if(j.length?j.get(0).parentNode.insertBefore(h,j[0]):u().head.appendChild(h),t&&i>=31)return void c.raise("You have reached the browser stylesheet limit (31)",!0)}if("function"==typeof g){var k=b("<s>").attr("id","galleria-loader").hide().appendTo(u().body);L.wait({until:function(){return k.height()>0},success:function(){k.remove(),g.call(h,h)},error:function(){k.remove(),c.raise("Theme CSS could not load after 20 sec. "+(c.QUIRK?"Your browser is in Quirks Mode, please add a correct doctype.":"Please download the latest theme."),!0)},timeout:5e3})}return h}}}(),M=function(a){return L.insertStyleTag(".galleria-videoicon{width:60px;height:60px;position:absolute;top:50%;left:50%;z-index:1;margin:-30px 0 0 -30px;cursor:pointer;background:#000;background:rgba(0,0,0,.8);border-radius:3px;-webkit-transition:all 150ms}.galleria-videoicon i{width:0px;height:0px;border-style:solid;border-width:10px 0 10px 16px;display:block;border-color:transparent transparent transparent #ffffff;margin:20px 0 0 22px}.galleria-image:hover .galleria-videoicon{background:#000}","galleria-videoicon"),b(L.create("galleria-videoicon")).html("<i></i>").appendTo(a).click(function(){b(this).siblings("img").mouseup()})},N=function(){var a=function(a,c,d,e){var f=this.getOptions("easing"),g=this.getStageWidth(),h={left:g*(a.rewind?-1:1)},i={left:0};d?(h.opacity=0,i.opacity=1):h.opacity=1,b(a.next).css(h),L.animate(a.next,i,{duration:a.speed,complete:function(a){return function(){c(),a.css({left:0})}}(b(a.next).add(a.prev)),queue:!1,easing:f}),e&&(a.rewind=!a.rewind),a.prev&&(h={left:0},i={left:g*(a.rewind?1:-1)},d&&(h.opacity=1,i.opacity=0),b(a.prev).css(h),L.animate(a.prev,i,{duration:a.speed,queue:!1,easing:f,complete:function(){b(this).css("opacity",0)}}))};return{active:!1,init:function(a,b,c){N.effects.hasOwnProperty(a)&&N.effects[a].call(this,b,c)},effects:{fade:function(a,c){b(a.next).css({opacity:0,left:0}),L.animate(a.next,{opacity:1},{duration:a.speed,complete:c}),a.prev&&(b(a.prev).css("opacity",1).show(),L.animate(a.prev,{opacity:0},{duration:a.speed}))},flash:function(a,c){b(a.next).css({opacity:0,left:0}),a.prev?L.animate(a.prev,{opacity:0},{duration:a.speed/2,complete:function(){L.animate(a.next,{opacity:1},{duration:a.speed,complete:c})}}):L.animate(a.next,{opacity:1},{duration:a.speed,complete:c})},pulse:function(a,c){a.prev&&b(a.prev).hide(),b(a.next).css({opacity:0,left:0}).show(),L.animate(a.next,{opacity:1},{duration:a.speed,complete:c})},slide:function(b,c){a.apply(this,L.array(arguments))},fadeslide:function(b,c){a.apply(this,L.array(arguments).concat([!0]))},doorslide:function(b,c){a.apply(this,L.array(arguments).concat([!1,!0]))}}}}();return D.listen(),b.event.special["click:fast"]={propagate:!0,add:function(c){var d=function(a){if(a.touches&&a.touches.length){var b=a.touches[0];return{x:b.pageX,y:b.pageY}}},e={touched:!1,touchdown:!1,coords:{x:0,y:0},evObj:{}};b(this).data({clickstate:e,timer:0}).on("touchstart.fast",function(c){a.clearTimeout(b(this).data("timer")),b(this).data("clickstate",{touched:!0,touchdown:!0,coords:d(c.originalEvent),evObj:c})}).on("touchmove.fast",function(a){var c=d(a.originalEvent),e=b(this).data("clickstate");Math.max(Math.abs(e.coords.x-c.x),Math.abs(e.coords.y-c.y))>6&&b(this).data("clickstate",b.extend(e,{touchdown:!1}))}).on("touchend.fast",function(d){var f=b(this);f.data("clickstate").touchdown&&c.handler.call(this,d),f.data("timer",a.setTimeout(function(){f.data("clickstate",e)},400))}).on("click.fast",function(a){if(b(this).data("clickstate").touched)return!1;b(this).data("clickstate",e),c.handler.call(this,a)})},remove:function(){b(this).off("touchstart.fast touchmove.fast touchend.fast click.fast")}},g.on("orientationchange",function(){b(this).resize()}),c=function(){var h=this;this._options={},this._playing=!1,this._playtime=5e3,this._active=null,this._queue={length:0},this._data=[],this._dom={},this._thumbnails=[],this._layers=[],this._initialized=!1,this._firstrun=!1,this._stageWidth=0,this._stageHeight=0,this._target=d,this._binds=[],this._id=parseInt(1e4*p.random(),10),b.each("container stage images image-nav image-nav-left image-nav-right info info-text info-title info-description thumbnails thumbnails-list thumbnails-container thumb-nav-left thumb-nav-right loader counter tooltip".split(" "),function(a,b){h._dom[b]=L.create("galleria-"+b)}),b.each("current total".split(" "),function(a,b){h._dom[b]=L.create("galleria-"+b,"span")});var i=this._keyboard={keys:{UP:38,DOWN:40,LEFT:37,RIGHT:39,RETURN:13,ESCAPE:27,BACKSPACE:8,SPACE:32},map:{},bound:!1,press:function(a){var b=a.keyCode||a.which;b in i.map&&"function"==typeof i.map[b]&&i.map[b].call(h,a)},attach:function(a){var b,c;for(b in a)a.hasOwnProperty(b)&&(c=b.toUpperCase(),c in i.keys?i.map[i.keys[c]]=a[b]:i.map[c]=a[b]);i.bound||(i.bound=!0,f.on("keydown",i.press))},detach:function(){i.bound=!1,i.map={},f.off("keydown",i.press)}},j=this._controls={0:d,1:d,active:0,swap:function(){j.active=j.active?0:1},getActive:function(){return h._options.swipe?j.slides[h._active]:j[j.active]},getNext:function(){return h._options.swipe?j.slides[h.getNext(h._active)]:j[1-j.active]},slides:[],frames:[],layers:[]},k=this._carousel={next:h.$("thumb-nav-right"),prev:h.$("thumb-nav-left"),width:0,current:0,max:0,hooks:[],update:function(){var a=0,c=0,d=[0];b.each(h._thumbnails,function(e,f){if(f.ready){a+=f.outerWidth||b(f.container).outerWidth(!0);var g=b(f.container).width();a+=g-p.floor(g),d[e+1]=a,c=p.max(c,f.outerHeight||b(f.container).outerHeight(!0))}}),h.$("thumbnails").css({width:a,height:c}),k.max=a,k.hooks=d,k.width=h.$("thumbnails-list").width(),k.setClasses(),h.$("thumbnails-container").toggleClass("galleria-carousel",a>k.width),k.width=h.$("thumbnails-list").width()},bindControls:function(){var a;k.next.on("click:fast",function(b){if(b.preventDefault(),"auto"===h._options.carouselSteps){for(a=k.current;a<k.hooks.length;a++)if(k.hooks[a]-k.hooks[k.current]>k.width){k.set(a-2);break}}else k.set(k.current+h._options.carouselSteps)}),k.prev.on("click:fast",function(b){if(b.preventDefault(),"auto"===h._options.carouselSteps)for(a=k.current;a>=0;a--){if(k.hooks[k.current]-k.hooks[a]>k.width){k.set(a+2);break}if(0===a){k.set(0);break}}else k.set(k.current-h._options.carouselSteps)})},set:function(a){for(a=p.max(a,0);k.hooks[a-1]+k.width>=k.max&&a>=0;)a--;k.current=a,k.animate()},getLast:function(a){return(a||k.current)-1},follow:function(a){if(0===a||a===k.hooks.length-2)return void k.set(a);for(var b=k.current;k.hooks[b]-k.hooks[k.current]<k.width&&b<=k.hooks.length;)b++;a-1<k.current?k.set(a-1):a+2>b&&k.set(a-b+k.current+2)},setClasses:function(){k.prev.toggleClass("disabled",!k.current),k.next.toggleClass("disabled",k.hooks[k.current]+k.width>=k.max)},animate:function(a){k.setClasses();var c=-1*k.hooks[k.current];isNaN(c)||(h.$("thumbnails").css("left",function(){return b(this).css("left")}),L.animate(h.get("thumbnails"),{left:c},{duration:h._options.carouselSpeed,easing:h._options.easing,queue:!1}))}},l=this._tooltip={initialized:!1,open:!1,timer:"tooltip"+h._id,swapTimer:"swap"+h._id,init:function(){l.initialized=!0;L.insertStyleTag(".galleria-tooltip{padding:3px 8px;max-width:50%;background:#ffe;color:#000;z-index:3;position:absolute;font-size:11px;line-height:1.3;opacity:0;box-shadow:0 0 2px rgba(0,0,0,.4);-moz-box-shadow:0 0 2px rgba(0,0,0,.4);-webkit-box-shadow:0 0 2px rgba(0,0,0,.4);}","galleria-tooltip"),h.$("tooltip").css({opacity:.8,visibility:"visible",display:"none"})},move:function(a){var b=h.getMousePosition(a).x,c=h.getMousePosition(a).y,d=h.$("tooltip"),e=b,f=c,g=d.outerHeight(!0)+1,i=d.outerWidth(!0),j=g+15,k=h.$("container").width()-i-2,l=h.$("container").height()-g-2;isNaN(e)||isNaN(f)||(e+=10,f-=g+8,e=p.max(0,p.min(k,e)),f=p.max(0,p.min(l,f)),c<j&&(f=j),d.css({left:e,top:f}))},bind:function(a,d){if(!c.TOUCH){l.initialized||l.init();var e=function(){h.$("container").off("mousemove",l.move),h.clearTimer(l.timer),h.$("tooltip").stop().animate({opacity:0},200,function(){h.$("tooltip").hide(),h.addTimer(l.swapTimer,function(){l.open=!1},1e3)})},f=function(a,c){l.define(a,c),b(a).hover(function(){h.clearTimer(l.swapTimer),h.$("container").off("mousemove",l.move).on("mousemove",l.move).trigger("mousemove"),l.show(a),h.addTimer(l.timer,function(){h.$("tooltip").stop().show().animate({opacity:1}),l.open=!0},l.open?0:500)},e).click(e)};"string"==typeof d?f(a in h._dom?h.get(a):a,d):b.each(a,function(a,b){f(h.get(a),b)})}},show:function(c){c=b(c in h._dom?h.get(c):c);var d=c.data("tt"),e=function(b){a.setTimeout(function(a){return function(){l.move(a)}}(b),10),c.off("mouseup",e)};(d="function"==typeof d?d():d)&&(h.$("tooltip").html(d.replace(/\s/,"&#160;")),c.on("mouseup",e))},define:function(a,c){if("function"!=typeof c){var d=c;c=function(){return d}}a=b(a in h._dom?h.get(a):a).data("tt",c),l.show(a)}},n=this._fullscreen={scrolled:0,crop:d,active:!1,prev:b(),beforeEnter:function(a){a()},beforeExit:function(a){a()},keymap:h._keyboard.map,parseCallback:function(a,c){return N.active?function(){"function"==typeof a&&a.call(h);var d=h._controls.getActive(),e=h._controls.getNext();h._scaleImage(e),h._scaleImage(d),c&&h._options.trueFullscreen&&b(d.container).add(e.container).trigger("transitionend")}:a},enter:function(b){n.beforeEnter(function(){b=n.parseCallback(b,!0),h._options.trueFullscreen&&D.support?(n.active=!0,L.forceStyles(h.get("container"),{width:"100%",height:"100%"}),h.rescale(),c.MAC?c.SAFARI&&/version\/[1-5]/.test(m)?(h.$("stage").css("opacity",0),a.setTimeout(function(){n.scale(),h.$("stage").css("opacity",1)},4)):(h.$("container").css("opacity",0).addClass("fullscreen"),a.setTimeout(function(){n.scale(),h.$("container").css("opacity",1)},50)):h.$("container").addClass("fullscreen"),g.resize(n.scale),D.enter(h,b,h.get("container"))):(n.scrolled=g.scrollTop(),c.TOUCH||a.scrollTo(0,0),n._enter(b))})},_enter:function(f){n.active=!0,v&&(n.iframe=function(){var d,f=e.referrer,g=e.createElement("a"),h=a.location;return g.href=f,g.protocol!=h.protocol||g.hostname!=h.hostname||g.port!=h.port?(c.raise("Parent fullscreen not available. Iframe protocol, domains and ports must match."),!1):(n.pd=a.parent.document,b(n.pd).find("iframe").each(function(){if((this.contentDocument||this.contentWindow.document)===e)return d=this,!1}),d)}()),L.hide(h.getActiveImage()),v&&n.iframe&&(n.iframe.scrolled=b(a.parent).scrollTop(),a.parent.scrollTo(0,0));var i=h.getData(),j=h._options,k=!h._options.trueFullscreen||!D.support,l={height:"100%",overflow:"hidden",margin:0,padding:0};if(k&&(h.$("container").addClass("fullscreen"),n.prev=h.$("container").prev(),n.prev.length||(n.parent=h.$("container").parent()),h.$("container").appendTo("body"),L.forceStyles(h.get("container"),{position:c.TOUCH?"absolute":"fixed",top:0,left:0,width:"100%",height:"100%",zIndex:1e4}),L.forceStyles(u().html,l),L.forceStyles(u().body,l)),v&&n.iframe&&(L.forceStyles(n.pd.documentElement,l),L.forceStyles(n.pd.body,l),L.forceStyles(n.iframe,b.extend(l,{width:"100%",height:"100%",top:0,left:0,position:"fixed",zIndex:1e4,border:"none"}))),n.keymap=b.extend({},h._keyboard.map),h.attachKeyboard({escape:h.exitFullscreen,right:h.next,left:h.prev}),n.crop=j.imageCrop,j.fullscreenCrop!=d&&(j.imageCrop=j.fullscreenCrop),i&&i.big&&i.image!==i.big){var m=new c.Picture,o=m.isCached(i.big),p=h.getIndex(),q=h._thumbnails[p];h.trigger({type:c.LOADSTART,cached:o,rewind:!1,index:p,imageTarget:h.getActiveImage(),thumbTarget:q,galleriaData:i}),m.load(i.big,function(a){h._scaleImage(a,{complete:function(a){h.trigger({type:c.LOADFINISH,cached:o,index:p,rewind:!1,imageTarget:a.image,thumbTarget:q});var d=h._controls.getActive().image;d&&(b(d).width(a.image.width).height(a.image.height).attr("style",b(a.image).attr("style")),a.image.src.srcset&&b(d).attr("srcset",a.image.src.srcset),a.image.src.sizes&&b(d).attr("sizes",a.image.src.sizes),b(d).attr("src",a.image.src))}})});var r=h.getNext(p),s=new c.Picture,t=h.getData(r);s.preload(h.isFullscreen()&&t.big?t.big:t.image)}h.rescale(function(){h.addTimer(!1,function(){k&&L.show(h.getActiveImage()),"function"==typeof f&&f.call(h),h.rescale()},100),h.trigger(c.FULLSCREEN_ENTER)}),k?g.resize(n.scale):L.show(h.getActiveImage())},scale:function(){h.rescale()},exit:function(a){n.beforeExit(function(){a=n.parseCallback(a),h._options.trueFullscreen&&D.support?D.exit(a):n._exit(a)})},_exit:function(b){n.active=!1;var d=!h._options.trueFullscreen||!D.support,e=h.$("container").removeClass("fullscreen");if(n.parent?n.parent.prepend(e):e.insertAfter(n.prev),d){L.hide(h.getActiveImage()),L.revertStyles(h.get("container"),u().html,u().body),c.TOUCH||a.scrollTo(0,n.scrolled);var f=h._controls.frames[h._controls.active];f&&f.image&&(f.image.src=f.image.src)}v&&n.iframe&&(L.revertStyles(n.pd.documentElement,n.pd.body,n.iframe),n.iframe.scrolled&&a.parent.scrollTo(0,n.iframe.scrolled)),h.detachKeyboard(),h.attachKeyboard(n.keymap),h._options.imageCrop=n.crop;var i=h.getData().big,j=h._controls.getActive().image;!h.getData().iframe&&j&&i&&i==j.src&&a.setTimeout(function(a){return function(){j.src=a}}(h.getData().image),1),h.rescale(function(){h.addTimer(!1,function(){d&&L.show(h.getActiveImage()),"function"==typeof b&&b.call(h),g.trigger("resize")},50),h.trigger(c.FULLSCREEN_EXIT)}),g.off("resize",n.scale)}},o=this._idle={trunk:[],bound:!1,active:!1,add:function(a,d,e,f){if(a&&!c.TOUCH){o.bound||o.addEvent(),a=b(a),"boolean"==typeof e&&(f=e,e={}),e=e||{};var g,h={};for(g in d)d.hasOwnProperty(g)&&(h[g]=a.css(g));a.data("idle",{from:b.extend(h,e),to:d,complete:!0,busy:!1}),f?a.css(d):o.addTimer(),o.trunk.push(a)}},remove:function(a){a=b(a),b.each(o.trunk,function(b,c){c&&c.length&&!c.not(a).length&&(a.css(a.data("idle").from),o.trunk.splice(b,1))}),o.trunk.length||(o.removeEvent(),h.clearTimer(o.timer))},addEvent:function(){o.bound=!0,h.$("container").on("mousemove click",o.showAll),"hover"==h._options.idleMode&&h.$("container").on("mouseleave",o.hide)},removeEvent:function(){o.bound=!1,h.$("container").on("mousemove click",o.showAll),"hover"==h._options.idleMode&&h.$("container").off("mouseleave",o.hide)},addTimer:function(){"hover"!=h._options.idleMode&&h.addTimer("idle",function(){o.hide()},h._options.idleTime)},hide:function(){if(h._options.idleMode&&!1!==h.getIndex()){h.trigger(c.IDLE_ENTER);var a=o.trunk.length;b.each(o.trunk,function(b,c){var d=c.data("idle");d&&(c.data("idle").complete=!1,L.animate(c,d.to,{duration:h._options.idleSpeed,complete:function(){b==a-1&&(o.active=!1)}}))})}},showAll:function(){h.clearTimer("idle"),b.each(o.trunk,function(a,b){o.show(b)})},show:function(a){var d=a.data("idle");o.active&&(d.busy||d.complete)||(d.busy=!0,h.trigger(c.IDLE_EXIT),h.clearTimer("idle"),L.animate(a,d.from,{duration:h._options.idleSpeed/2,complete:function(){o.active=!0,b(a).data("idle").busy=!1,b(a).data("idle").complete=!0}})),o.addTimer()}},q=this._lightbox={width:0,height:0,initialized:!1,active:null,image:null,elems:{},keymap:!1,init:function(){if(!q.initialized){q.initialized=!0;var a={},d=h._options,e="",f="position:absolute;",g="lightbox-",i={overlay:"position:fixed;display:none;opacity:"+d.overlayOpacity+";filter:alpha(opacity="+100*d.overlayOpacity+");top:0;left:0;width:100%;height:100%;background:"+d.overlayBackground+";z-index:99990",box:"position:fixed;display:none;width:400px;height:400px;top:50%;left:50%;margin-top:-200px;margin-left:-200px;z-index:99991",shadow:f+"background:#000;width:100%;height:100%;",content:f+"background-color:#fff;top:10px;left:10px;right:10px;bottom:10px;overflow:hidden",info:f+"bottom:10px;left:10px;right:10px;color:#444;font:11px/13px arial,sans-serif;height:13px",close:f+"top:10px;right:10px;height:20px;width:20px;background:#fff;text-align:center;cursor:pointer;color:#444;font:16px/22px arial,sans-serif;z-index:99999",image:f+"top:10px;left:10px;right:10px;bottom:30px;overflow:hidden;display:block;",prevholder:f+"width:50%;top:0;bottom:40px;cursor:pointer;",nextholder:f+"width:50%;top:0;bottom:40px;right:-1px;cursor:pointer;",prev:f+"top:50%;margin-top:-20px;height:40px;width:30px;background:#fff;left:20px;display:none;text-align:center;color:#000;font:bold 16px/36px arial,sans-serif",next:f+"top:50%;margin-top:-20px;height:40px;width:30px;background:#fff;right:20px;left:auto;display:none;font:bold 16px/36px arial,sans-serif;text-align:center;color:#000",title:"float:left",counter:"float:right;margin-left:8px;"},j={},k="";k=t>7?t<9?"background:#000;filter:alpha(opacity=0);":"background:rgba(0,0,0,0);":"z-index:99999",i.nextholder+=k,i.prevholder+=k,b.each(i,function(a,b){e+=".galleria-"+g+a+"{"+b+"}"}),e+=".galleria-lightbox-box.iframe .galleria-lightbox-prevholder,.galleria-lightbox-box.iframe .galleria-lightbox-nextholder{width:100px;height:100px;top:50%;margin-top:-70px}",L.insertStyleTag(e,"galleria-lightbox"),b.each("overlay box content shadow title info close prevholder prev nextholder next counter image".split(" "),function(b,c){h.addElement("lightbox-"+c),a[c]=q.elems[c]=h.get("lightbox-"+c)}),q.image=new c.Picture,b.each({box:"shadow content close prevholder nextholder",info:"title counter",content:"info image",prevholder:"prev",nextholder:"next"},function(a,c){var d=[];b.each(c.split(" "),function(a,b){d.push(g+b)}),j[g+a]=d}),h.append(j),b(a.image).append(q.image.container),b(u().body).append(a.overlay,a.box),function(a){a.hover(function(){b(this).css("color","#bbb")},function(){b(this).css("color","#444")})}(b(a.close).on("click:fast",q.hide).html("&#215;")),b.each(["Prev","Next"],function(d,e){var f=b(a[e.toLowerCase()]).html(/v/.test(e)?"&#8249;&#160;":"&#160;&#8250;"),g=b(a[e.toLowerCase()+"holder"]);if(g.on("click:fast",function(){q["show"+e]()}),t<8||c.TOUCH)return void f.show();g.hover(function(){f.show()},function(a){f.stop().fadeOut(200)})}),b(a.overlay).on("click:fast",q.hide),c.IPAD&&(h._options.lightboxTransitionSpeed=0)}},rescale:function(a){var d=p.min(g.width()-40,q.width),e=p.min(g.height()-60,q.height),f=p.min(d/q.width,e/q.height),i=p.round(q.width*f)+40,j=p.round(q.height*f)+60,k={width:i,height:j,"margin-top":-1*p.ceil(j/2),"margin-left":-1*p.ceil(i/2)};a?b(q.elems.box).css(k):b(q.elems.box).animate(k,{duration:h._options.lightboxTransitionSpeed,easing:h._options.easing,complete:function(){var a=q.image,d=h._options.lightboxFadeSpeed;h.trigger({type:c.LIGHTBOX_IMAGE,imageTarget:a.image}),b(a.container).show(),b(a.image).animate({opacity:1},d),L.show(q.elems.info,d)}})},hide:function(){q.image.image=null,g.off("resize",q.rescale),b(q.elems.box).hide().find("iframe").remove(),L.hide(q.elems.info),h.detachKeyboard(),h.attachKeyboard(q.keymap),q.keymap=!1,L.hide(q.elems.overlay,200,function(){b(this).hide().css("opacity",h._options.overlayOpacity),h.trigger(c.LIGHTBOX_CLOSE)})},showNext:function(){q.show(h.getNext(q.active))},showPrev:function(){q.show(h.getPrev(q.active))},show:function(d){q.active=d="number"==typeof d?d:h.getIndex()||0,q.initialized||q.init(),h.trigger(c.LIGHTBOX_OPEN),q.keymap||(q.keymap=b.extend({},h._keyboard.map),h.attachKeyboard({escape:q.hide,right:q.showNext,left:q.showPrev})),g.off("resize",q.rescale);var e,f,i,j=h.getData(d),k=h.getDataLength(),l=h.getNext(d);L.hide(q.elems.info);try{for(i=h._options.preload;i>0;i--)f=new c.Picture,e=h.getData(l),f.preload(e.big?e.big:e.image),l=h.getNext(l)}catch(a){}q.image.isIframe=j.iframe&&!j.image,b(q.elems.box).toggleClass("iframe",q.image.isIframe),b(q.image.container).find(".galleria-videoicon").remove(),q.image.load(j.big||j.image||j.iframe,function(c){if(c.isIframe){var e=b(a).width(),f=b(a).height();if(c.video&&h._options.maxVideoSize){var i=p.min(h._options.maxVideoSize/e,h._options.maxVideoSize/f);i<1&&(e*=i,f*=i)}q.width=e,q.height=f}else q.width=c.original.width,q.height=c.original.height;if(b(c.image).css({width:c.isIframe?"100%":"100.1%",height:c.isIframe?"100%":"100.1%",top:0,bottom:0,zIndex:99998,opacity:0,visibility:"visible"}).parent().height("100%"),q.elems.title.innerHTML=j.title||"",q.elems.counter.innerHTML=d+1+" / "+k,g.resize(q.rescale),q.rescale(),j.image&&j.iframe){if(b(q.elems.box).addClass("iframe"),j.video){var l=M(c.container).hide();a.setTimeout(function(){l.fadeIn(200)},200)}b(c.image).css("cursor","pointer").mouseup(function(a,c){return function(d){b(q.image.container).find(".galleria-videoicon").remove(),d.preventDefault(),c.isIframe=!0,c.load(a.iframe+(a.video?"&autoplay=1":""),{width:"100%",height:t<8?b(q.image.container).height():"100%"})}}(j,c))}}),b(q.elems.overlay).show().css("visibility","visible"),b(q.elems.box).show()}},r=this._timer={trunk:{},add:function(b,c,d,e){if(b=b||(new Date).getTime(),e=e||!1,this.clear(b),e){var f=c;c=function(){f(),r.add(b,c,d)}}this.trunk[b]=a.setTimeout(c,d)},clear:function(b){var c,d=function(b){a.clearTimeout(this.trunk[b]),delete this.trunk[b]};if(b&&b in this.trunk)d.call(this,b);else if(void 0===b)for(c in this.trunk)this.trunk.hasOwnProperty(c)&&d.call(this,c)}};return this},c.prototype={constructor:c,init:function(a,e){if(e=y(e),this._original={target:a,options:e,data:null},this._target=this._dom.target=a.nodeName?a:b(a).get(0),this._original.html=this._target.innerHTML,F.push(this),!this._target)return void c.raise("Target not found",!0);if(this._options={autoplay:!1,carousel:!0,carouselFollow:!0,carouselSpeed:400,carouselSteps:"auto",clicknext:!1,dailymotion:{foreground:"%23EEEEEE",highlight:"%235BCEC5",background:"%23222222",logo:0,hideInfos:1},dataConfig:function(a){return{}},dataSelector:"img",dataSort:!1,dataSource:this._target,debug:d,dummy:d,easing:"galleria",extend:function(a){},fullscreenCrop:d,fullscreenDoubleTap:!0,fullscreenTransition:d,height:0,idleMode:!0,idleTime:3e3,idleSpeed:200,imageCrop:!1,imageMargin:0,imagePan:!1,imagePanSmoothness:12,imagePosition:"50%",imageTimeout:d,initialTransition:d,keepSource:!1,layerFollow:!0,lightbox:!1,lightboxFadeSpeed:200,lightboxTransitionSpeed:200,linkSourceImages:!0,maxScaleRatio:d,maxVideoSize:d,minScaleRatio:d,overlayOpacity:.85,overlayBackground:"#0b0b0b",pauseOnInteraction:!0,popupLinks:!1,preload:2,queue:!0,responsive:!0,show:0,showInfo:!0,showCounter:!0,showImagenav:!0,swipe:"auto",theme:null,thumbCrop:!0,thumbEventType:"click:fast",thumbMargin:0,thumbQuality:"auto",thumbDisplayOrder:!0,
thumbPosition:"50%",thumbnails:!0,touchTransition:d,transition:"fade",transitionInitial:d,transitionSpeed:400,trueFullscreen:!0,useCanvas:!1,variation:"",videoPoster:!0,vimeo:{title:0,byline:0,portrait:0,color:"aaaaaa"},wait:5e3,width:"auto",youtube:{modestbranding:1,autohide:1,color:"white",hd:1,rel:0,showinfo:0}},this._options.initialTransition=this._options.initialTransition||this._options.transitionInitial,e&&(!1===e.debug&&(j=!1),"number"==typeof e.imageTimeout&&(k=e.imageTimeout),"string"==typeof e.dummy&&(l=e.dummy),"string"==typeof e.theme&&(this._options.theme=e.theme)),b(this._target).children().hide(),c.QUIRK&&c.raise("Your page is in Quirks mode, Galleria may not render correctly. Please validate your HTML and add a correct doctype."),J.length)if(this._options.theme){for(var f=0;f<J.length;f++)if(this._options.theme===J[f].name){this.theme=J[f];break}}else this.theme=J[0];return"object"==typeof this.theme?this._init():I.push(this),this},_init:function(){var f=this,h=this._options;if(this._initialized)return c.raise("Init failed: Gallery instance already initialized."),this;if(this._initialized=!0,!this.theme)return c.raise("Init failed: No theme found.",!0),this;if(b.extend(!0,h,this.theme.defaults,this._original.options,c.configure.options),h.swipe=function(a){return"enforced"==a||!1!==a&&"disabled"!=a&&!!c.TOUCH}(h.swipe),h.swipe&&(h.clicknext=!1,h.imagePan=!1),function(a){if(!("getContext"in a))return void(a=null);H=H||{elem:a,context:a.getContext("2d"),cache:{},length:0}}(e.createElement("canvas")),this.bind(c.DATA,function(){a.screen&&a.screen.width&&Array.prototype.forEach&&this._data.forEach(function(b){var c="devicePixelRatio"in a?a.devicePixelRatio:1;p.max(a.screen.width,a.screen.height)*c<1024&&(b.big=b.image)}),this._original.data=this._data,this.get("total").innerHTML=this.getDataLength();var b=this.$("container");f._options.height<2&&(f._userRatio=f._ratio=f._options.height);var d={width:0,height:0},e=function(){return f.$("stage").height()};L.wait({until:function(){return d=f._getWH(),b.width(d.width).height(d.height),e()&&d.width&&d.height>50},success:function(){f._width=d.width,f._height=d.height,f._ratio=f._ratio||d.height/d.width,c.WEBKIT?a.setTimeout(function(){f._run()},1):f._run()},error:function(){e()?c.raise("Could not extract sufficient width/height of the gallery container. Traced measures: width:"+d.width+"px, height: "+d.height+"px.",!0):c.raise("Could not extract a stage height from the CSS. Traced height: "+e()+"px.",!0)},timeout:"number"==typeof this._options.wait&&this._options.wait})}),this.append({"info-text":["info-title","info-description"],info:["info-text"],"image-nav":["image-nav-right","image-nav-left"],stage:["images","loader","counter","image-nav"],"thumbnails-list":["thumbnails"],"thumbnails-container":["thumb-nav-left","thumbnails-list","thumb-nav-right"],container:["stage","thumbnails-container","info","tooltip"]}),L.hide(this.$("counter").append(this.get("current"),e.createTextNode(" / "),this.get("total"))),this.setCounter("&#8211;"),L.hide(f.get("tooltip")),this.$("container").addClass([c.TOUCH?"touch":"notouch",this._options.variation,"galleria-theme-"+this.theme.name].join(" ")),this._options.swipe||b.each(new Array(2),function(a){var d=new c.Picture;b(d.container).css({position:"absolute",top:0,left:0}).prepend(f._layers[a]=b(L.create("galleria-layer")).css({position:"absolute",top:0,left:0,right:0,bottom:0,zIndex:2})[0]),f.$("images").append(d.container),f._controls[a]=d;var e=new c.Picture;e.isIframe=!0,b(e.container).attr("class","galleria-frame").css({position:"absolute",top:0,left:0,zIndex:4,background:"#000",display:"none"}).appendTo(d.container),f._controls.frames[a]=e}),this.$("images").css({position:"relative",top:0,left:0,width:"100%",height:"100%"}),h.swipe&&(this.$("images").css({position:"absolute",top:0,left:0,width:0,height:"100%"}),this.finger=new c.Finger(this.get("stage"),{onchange:function(a){f.pause().show(a)},oncomplete:function(a){var c=p.max(0,p.min(parseInt(a,10),f.getDataLength()-1)),d=f.getData(c);b(f._thumbnails[c].container).addClass("active").siblings(".active").removeClass("active"),d&&(f.$("images").find(".galleria-frame").css("opacity",0).hide().find("iframe").remove(),f._options.carousel&&f._options.carouselFollow&&f._carousel.follow(c))}}),this.bind(c.RESCALE,function(){this.finger.setup()}),this.$("stage").on("click",function(c){var e=f.getData();if(e){if(e.iframe){f.isPlaying()&&f.pause();var g=f._controls.frames[f._active],h=f._stageWidth,i=f._stageHeight;if(b(g.container).find("iframe").length)return;return b(g.container).css({width:h,height:i,opacity:0}).show().animate({opacity:1},200),void a.setTimeout(function(){g.load(e.iframe+(e.video?"&autoplay=1":""),{width:h,height:i},function(a){f.$("container").addClass("videoplay"),a.scale({width:f._stageWidth,height:f._stageHeight,iframelimit:e.video?f._options.maxVideoSize:d})})},100)}if(e.link)if(f._options.popupLinks){a.open(e.link,"_blank")}else a.location.href=e.link;else;}}),this.bind(c.IMAGE,function(a){f.setCounter(a.index),f.setInfo(a.index);var c=this.getNext(),d=this.getPrev(),e=[d,c];e.push(this.getNext(c),this.getPrev(d),f._controls.slides.length-1);var g=[];b.each(e,function(a,c){-1==b.inArray(c,g)&&g.push(c)}),b.each(g,function(a,c){var d=f.getData(c),e=f._controls.slides[c],g=f.isFullscreen()&&d.big?d.big:d.image||d.iframe;d.iframe&&!d.image&&(e.isIframe=!0),e.ready||f._controls.slides[c].load(g,function(a){a.isIframe||b(a.image).css("visibility","hidden"),f._scaleImage(a,{complete:function(a){a.isIframe||b(a.image).css({opacity:0,visibility:"visible"}).animate({opacity:1},200)}})})})})),this.$("thumbnails, thumbnails-list").css({overflow:"hidden",position:"relative"}),this.$("image-nav-right, image-nav-left").on("click:fast",function(a){h.pauseOnInteraction&&f.pause();var b=/right/.test(this.className)?"next":"prev";f[b]()}).on("click",function(a){a.preventDefault(),(h.clicknext||h.swipe)&&a.stopPropagation()}),b.each(["info","counter","image-nav"],function(a,b){!1===h["show"+b.substr(0,1).toUpperCase()+b.substr(1).replace(/-/,"")]&&L.moveOut(f.get(b.toLowerCase()))}),this.load(),h.keepSource||t||(this._target.innerHTML=""),this.get("errors")&&this.appendChild("target","errors"),this.appendChild("target","container"),h.carousel){var i=0,j=h.show;this.bind(c.THUMBNAIL,function(){this.updateCarousel(),++i==this.getDataLength()&&"number"==typeof j&&j>0&&this._carousel.follow(j)})}return h.responsive&&g.on("resize",function(){f.isFullscreen()||f.resize()}),h.fullscreenDoubleTap&&this.$("stage").on("touchstart",function(){var a,b,c,d,e,g,h=function(a){return a.originalEvent.touches?a.originalEvent.touches[0]:a};return f.$("stage").on("touchmove",function(){a=0}),function(i){if(!/(-left|-right)/.test(i.target.className)){if(g=L.timestamp(),b=h(i).pageX,c=h(i).pageY,i.originalEvent.touches.length<2&&g-a<300&&b-d<20&&c-e<20)return f.toggleFullscreen(),void i.preventDefault();a=g,d=b,e=c}}}()),b.each(c.on.binds,function(a,c){-1==b.inArray(c.hash,f._binds)&&f.bind(c.type,c.callback)}),this},addTimer:function(){return this._timer.add.apply(this._timer,L.array(arguments)),this},clearTimer:function(){return this._timer.clear.apply(this._timer,L.array(arguments)),this},_getWH:function(){var a,c=this.$("container"),d=this.$("target"),e=this,f={};return b.each(["width","height"],function(b,g){e._options[g]&&"number"==typeof e._options[g]?f[g]=e._options[g]:(a=[L.parseValue(c.css(g)),L.parseValue(d.css(g)),c[g](),d[g]()],e["_"+g]||a.splice(a.length,L.parseValue(c.css("min-"+g)),L.parseValue(d.css("min-"+g))),f[g]=p.max.apply(p,a))}),e._userRatio&&(f.height=f.width*e._userRatio),f},_createThumbnails:function(d){this.get("total").innerHTML=this.getDataLength();var f,g,h,i,j=this,k=this._options,l=d?this._data.length-d.length:0,m=l,n=[],o=0,p=t<8?"http://upload.wikimedia.org/wikipedia/commons/c/c0/Blank.gif":"data:image/gif;base64,R0lGODlhAQABAPABAP///wAAACH5BAEKAAAALAAAAAABAAEAAAICRAEAOw%3D%3D",q=function(){var a=j.$("thumbnails").find(".active");return!!a.length&&a.find("img").attr("src")}(),r="string"==typeof k.thumbnails?k.thumbnails.toLowerCase():null,s=function(a){return e.defaultView&&e.defaultView.getComputedStyle?e.defaultView.getComputedStyle(g.container,null)[a]:i.css(a)},u=function(a){k.pauseOnInteraction&&j.pause();var c=b(a.currentTarget).data("index");j.getIndex()!==c&&j.show(c),a.preventDefault()},v=function(a,d){b(a.container).css("visibility","visible"),j.trigger({type:c.THUMBNAIL,thumbTarget:a.image,index:a.data.order,galleriaData:j.getData(a.data.order)}),"function"==typeof d&&d.call(j,a)},w=function(a,c){a.scale({width:a.data.width,height:a.data.height,crop:k.thumbCrop,margin:k.thumbMargin,canvas:k.useCanvas,position:k.thumbPosition,complete:function(a){var d,e,f=["left","top"],g=["Width","Height"];j.getData(a.index);b.each(g,function(c,g){d=g.toLowerCase(),!0===k.thumbCrop&&k.thumbCrop!==d||(e={},e[d]=a[d],b(a.container).css(e),e={},e[f[c]]=0,b(a.image).css(e)),a["outer"+g]=b(a.container)["outer"+g](!0)}),L.toggleQuality(a.image,!0===k.thumbQuality||"auto"===k.thumbQuality&&a.original.width<3*a.width),k.thumbDisplayOrder&&!a.lazy?b.each(n,function(a,b){if(a===o&&b.ready&&!b.displayed)return o++,b.displayed=!0,void v(b,c)}):v(a,c)}})};for(d||(this._thumbnails=[],this.$("thumbnails").empty());this._data[l];l++)h=this._data[l],f=h.thumb||h.image,!0!==k.thumbnails&&"lazy"!=r||!h.thumb&&!h.image?h.iframe&&null!==r||"empty"===r||"numbers"===r?(g={container:L.create("galleria-image"),image:L.create("img","span"),ready:!0,data:{order:l}},"numbers"===r&&b(g.image).text(l+1),h.iframe&&b(g.image).addClass("iframe"),this.$("thumbnails").append(g.container),a.setTimeout(function(a,d,e){return function(){b(e).append(a),j.trigger({type:c.THUMBNAIL,thumbTarget:a,index:d,galleriaData:j.getData(d)})}}(g.image,l,g.container),50+20*l)):g={container:null,image:null}:(g=new c.Picture(l),g.index=l,g.displayed=!1,g.lazy=!1,g.video=!1,this.$("thumbnails").append(g.container),i=b(g.container),i.css("visibility","hidden"),g.data={width:L.parseValue(s("width")),height:L.parseValue(s("height")),order:l,src:f},!0!==k.thumbCrop?i.css({width:"auto",height:"auto"}):i.css({width:g.data.width,height:g.data.height}),"lazy"==r?(i.addClass("lazy"),g.lazy=!0,g.load(p,{height:g.data.height,width:g.data.width})):g.load(f,w),"all"===k.preload&&g.preload(h.image)),b(g.container).add(k.keepSource&&k.linkSourceImages?h.original:null).data("index",l).on(k.thumbEventType,u).data("thumbload",w),q===f&&b(g.container).addClass("active"),this._thumbnails.push(g);return n=this._thumbnails.slice(m),this},lazyLoad:function(a,c){var d=a.constructor==Array?a:[a],e=this,f=0;return b.each(d,function(a,g){if(!(g>e._thumbnails.length-1)){var h=e._thumbnails[g],i=h.data,j=function(){++f==d.length&&"function"==typeof c&&c.call(e)},k=b(h.container).data("thumbload");k&&(h.video?k.call(e,h,j):h.load(i.src,function(a){k.call(e,a,j)}))}}),this},lazyLoadChunks:function(b,c){var d=this.getDataLength(),e=0,f=0,g=[],h=[],i=this;for(c=c||0;e<d;e++)h.push(e),++f!=b&&e!=d-1||(g.push(h),f=0,h=[]);var j=function(b){var d=g.shift();d&&a.setTimeout(function(){i.lazyLoad(d,function(){j(!0)})},c&&b?c:0)};return j(!1),this},_run:function(){var e=this;e._createThumbnails(),L.wait({timeout:1e4,until:function(){return c.OPERA&&e.$("stage").css("display","inline-block"),e._stageWidth=e.$("stage").width(),e._stageHeight=e.$("stage").height(),e._stageWidth&&e._stageHeight>50},success:function(){if(E.push(e),e._options.swipe){var f=e.$("images").width(e.getDataLength()*e._stageWidth);b.each(new Array(e.getDataLength()),function(a){var d=new c.Picture,g=e.getData(a);b(d.container).css({position:"absolute",top:0,left:e._stageWidth*a}).prepend(e._layers[a]=b(L.create("galleria-layer")).css({position:"absolute",top:0,left:0,right:0,bottom:0,zIndex:2})[0]).appendTo(f),g.video&&M(d.container),e._controls.slides.push(d);var h=new c.Picture;h.isIframe=!0,b(h.container).attr("class","galleria-frame").css({position:"absolute",top:0,left:0,zIndex:4,background:"#000",display:"none"}).appendTo(d.container),e._controls.frames.push(h)}),e.finger.setup()}if(L.show(e.get("counter")),e._options.carousel&&e._carousel.bindControls(),e._options.autoplay&&(e.pause(),"number"==typeof e._options.autoplay&&(e._playtime=e._options.autoplay),e._playing=!0),e._firstrun)return e._options.autoplay&&e.trigger(c.PLAY),void("number"==typeof e._options.show&&e.show(e._options.show));e._firstrun=!0,c.History&&c.History.change(function(b){isNaN(b)?a.history.go(-1):e.show(b,d,!0)}),e.trigger(c.READY),e.theme.init.call(e,e._options),b.each(c.ready.callbacks,function(a,b){"function"==typeof b&&b.call(e,e._options)}),e._options.extend.call(e,e._options),/^[0-9]{1,4}$/.test(n)&&c.History?e.show(n,d,!0):e._data[e._options.show]&&e.show(e._options.show),e._options.autoplay&&e.trigger(c.PLAY)},error:function(){c.raise("Stage width or height is too small to show the gallery. Traced measures: width:"+e._stageWidth+"px, height: "+e._stageHeight+"px.",!0)}})},load:function(a,d,e){var f=this,g=this._options;return this._data=[],this._thumbnails=[],this.$("thumbnails").empty(),"function"==typeof d&&(e=d,d=null),a=a||g.dataSource,d=d||g.dataSelector,e=e||g.dataConfig,b.isPlainObject(a)&&(a=[a]),b.isArray(a)?this.validate(a)?this._data=a:c.raise("Load failed: JSON Array not valid."):(d+=",.video,.iframe",b(a).find(d).each(function(a,c){c=b(c);var d={},g=c.parent(),h=g.attr("href"),i=g.attr("rel");h&&("IMG"==c[0].nodeName||c.hasClass("video"))&&C(h)?d.video=h:h&&c.hasClass("iframe")?d.iframe=h:d.image=h,i&&(d.big=i),d.imagesrcset=g.data("srcset"),d.imagesizes=g.data("sizes"),d.thumbsizes=c.attr("sizes"),d.thumbsrcset=c.attr("srcset"),b.each("big bigsrcset bigsizes title description link layer image imagesrcset imagesizes".split(" "),function(a,b){c.data(b)&&(d[b]=c.data(b).toString())}),c.data("srcset")&&(d.imagesrcset=c.data("srcset")),c.data("sizes")&&(d.imagesizes=c.data("sizes")),d.big||(d.big=d.image,d.bigsrcset=d.imagesrcset,d.bigsizes=d.imagesizes),f._data.push(b.extend({title:c.attr("title")||"",thumb:c.attr("src"),image:c.attr("src"),big:c.attr("src"),description:c.attr("alt")||"",link:c.attr("longdesc"),original:c.get(0)},d,e(c)))})),"function"==typeof g.dataSort?i.sort.call(this._data,g.dataSort):"random"==g.dataSort&&this._data.sort(function(){return p.round(p.random())-.5}),this.getDataLength()&&this._parseData(function(){this.trigger(c.DATA)}),this},_parseData:function(a){var c,e=this,f=!1,g=function(){var c=!0;b.each(e._data,function(a,b){if(b.loading)return c=!1,!1}),c&&!f&&(f=!0,a.call(e))};return b.each(this._data,function(a,f){if(c=e._data[a],b.each("big image thumb".split(" "),function(a,b){f[b]&&(f[b]=new String(f[b]),f[b].srcset=f[b+"srcset"],f[b].sizes=f[b+"sizes"])}),"thumb"in f==!1&&(c.thumb=f.image),f.big||(c.big=f.image),"video"in f){var h=C(f.video);h&&(c.iframe=new B(h.provider,h.id).embed()+function(){if("object"==typeof e._options[h.provider]){var a=[];return b.each(e._options[h.provider],function(b,c){a.push(b+"="+c)}),"youtube"==h.provider&&(a=["wmode=opaque"].concat(a)),"?"+a.join("&")}return""}(),c.thumb&&c.image||b.each(["thumb","image"],function(a,b){if("image"==b&&!e._options.videoPoster)return void(c.image=d);var f=new B(h.provider,h.id);c[b]||(c.loading=!0,f.getMedia(b,function(a,b){return function(c){a[b]=c,"image"!=b||a.big||(a.big=a.image),delete a.loading,g()}}(c,b)))}))}}),g(),this},destroy:function(){return this.$("target").data("galleria",null),this.$("container").off("galleria"),this.get("target").innerHTML=this._original.html,this.clearTimer(),L.removeFromArray(F,this),L.removeFromArray(E,this),A._inst=[],void 0!==c._waiters&&c._waiters.length&&b.each(c._waiters,function(b,c){c&&a.clearTimeout(c)}),this},splice:function(){var b=this,c=L.array(arguments);return a.setTimeout(function(){i.splice.apply(b._data,c),b._parseData(function(){b._createThumbnails()})},2),b},push:function(){var b=this,c=L.array(arguments);return 1==c.length&&c[0].constructor==Array&&(c=c[0]),a.setTimeout(function(){i.push.apply(b._data,c),b._parseData(function(){b._createThumbnails(c)})},2),b},_getActive:function(){return this._controls.getActive()},validate:function(a){return!0},bind:function(a,b){return a=z(a),this.$("container").on(a,this.proxy(b)),this},unbind:function(a){return a=z(a),this.$("container").off(a),this},trigger:function(a){return a="object"==typeof a?b.extend(a,{scope:this}):{type:z(a),scope:this},this.$("container").trigger(a),this},addIdleState:function(a,b,c,d){return this._idle.add.apply(this._idle,L.array(arguments)),this},removeIdleState:function(a){return this._idle.remove.apply(this._idle,L.array(arguments)),this},enterIdleMode:function(){return this._idle.hide(),this},exitIdleMode:function(){return this._idle.showAll(),this},enterFullscreen:function(a){return this._fullscreen.enter.apply(this,L.array(arguments)),this},exitFullscreen:function(a){return this._fullscreen.exit.apply(this,L.array(arguments)),this},toggleFullscreen:function(a){return this._fullscreen[this.isFullscreen()?"exit":"enter"].apply(this,L.array(arguments)),this},bindTooltip:function(a,b){return this._tooltip.bind.apply(this._tooltip,L.array(arguments)),this},defineTooltip:function(a,b){return this._tooltip.define.apply(this._tooltip,L.array(arguments)),this},refreshTooltip:function(a){return this._tooltip.show.apply(this._tooltip,L.array(arguments)),this},openLightbox:function(){return this._lightbox.show.apply(this._lightbox,L.array(arguments)),this},closeLightbox:function(){return this._lightbox.hide.apply(this._lightbox,L.array(arguments)),this},hasVariation:function(a){return b.inArray(a,this._options.variation.split(/\s+/))>-1},getActiveImage:function(){var a=this._getActive();return a?a.image:d},getActiveThumb:function(){return this._thumbnails[this._active].image||d},getMousePosition:function(a){return{x:a.pageX-this.$("container").offset().left,y:a.pageY-this.$("container").offset().top}},addPan:function(a){if(!1!==this._options.imageCrop){a=b(a||this.getActiveImage());var c=this,d=a.width()/2,e=a.height()/2,f=parseInt(a.css("left"),10),g=parseInt(a.css("top"),10),h=f||0,i=g||0,j=0,k=0,l=!1,m=L.timestamp(),n=0,o=0,q=function(b,c,d){if(b>0&&(o=p.round(p.max(-1*b,p.min(0,c))),n!==o))if(n=o,8===t)a.parent()["scroll"+d](-1*o);else{var e={};e[d.toLowerCase()]=o,a.css(e)}},r=function(a){L.timestamp()-m<50||(l=!0,d=c.getMousePosition(a).x,e=c.getMousePosition(a).y)},s=function(b){l&&(j=a.width()-c._stageWidth,k=a.height()-c._stageHeight,f=d/c._stageWidth*j*-1,g=e/c._stageHeight*k*-1,h+=(f-h)/c._options.imagePanSmoothness,i+=(g-i)/c._options.imagePanSmoothness,q(k,i,"Top"),q(j,h,"Left"))};return 8===t&&(a.parent().scrollTop(-1*i).scrollLeft(-1*h),a.css({top:0,left:0})),this.$("stage").off("mousemove",r).on("mousemove",r),this.addTimer("pan"+c._id,s,50,!0),this}},proxy:function(a,b){return"function"!=typeof a?q:(b=b||this,function(){return a.apply(b,L.array(arguments))})},getThemeName:function(){return this.theme.name},removePan:function(){return this.$("stage").off("mousemove"),this.clearTimer("pan"+this._id),this},addElement:function(a){var c=this._dom;return b.each(L.array(arguments),function(a,b){c[b]=L.create("galleria-"+b)}),this},attachKeyboard:function(a){return this._keyboard.attach.apply(this._keyboard,L.array(arguments)),this},detachKeyboard:function(){return this._keyboard.detach.apply(this._keyboard,L.array(arguments)),this},appendChild:function(a,b){return this.$(a).append(this.get(b)||b),this},prependChild:function(a,b){return this.$(a).prepend(this.get(b)||b),this},remove:function(a){return this.$(L.array(arguments).join(",")).remove(),this},append:function(a){var b,c;for(b in a)if(a.hasOwnProperty(b))if(a[b].constructor===Array)for(c=0;a[b][c];c++)this.appendChild(b,a[b][c]);else this.appendChild(b,a[b]);return this},_scaleImage:function(a,c){if(a=a||this._controls.getActive()){var d,e=function(a){b(a.container).children(":first").css({top:p.max(0,L.parseValue(a.image.style.top)),left:p.max(0,L.parseValue(a.image.style.left)),width:L.parseValue(a.image.width),height:L.parseValue(a.image.height)})};return c=b.extend({width:this._stageWidth,height:this._stageHeight,crop:this._options.imageCrop,max:this._options.maxScaleRatio,min:this._options.minScaleRatio,margin:this._options.imageMargin,position:this._options.imagePosition,iframelimit:this._options.maxVideoSize},c),this._options.layerFollow&&!0!==this._options.imageCrop?"function"==typeof c.complete?(d=c.complete,c.complete=function(){d.call(a,a),e(a)}):c.complete=e:b(a.container).children(":first").css({top:0,left:0}),a.scale(c),this}},updateCarousel:function(){return this._carousel.update(),this},resize:function(a,c){"function"==typeof a&&(c=a,a=d),a=b.extend({width:0,height:0},a);var e=this,f=this.$("container");return b.each(a,function(b,c){c||(f[b]("auto"),a[b]=e._getWH()[b])}),b.each(a,function(a,b){f[a](b)}),this.rescale(c)},rescale:function(a,e,f){var g=this;return"function"==typeof a&&(f=a,a=d),function(){g._stageWidth=a||g.$("stage").width(),g._stageHeight=e||g.$("stage").height(),g._options.swipe?(b.each(g._controls.slides,function(a,c){g._scaleImage(c),b(c.container).css("left",g._stageWidth*a)}),g.$("images").css("width",g._stageWidth*g.getDataLength())):g._scaleImage(),g._options.carousel&&g.updateCarousel(),g._controls.frames[g._controls.active]&&g._controls.frames[g._controls.active].scale({width:g._stageWidth,height:g._stageHeight,iframelimit:g._options.maxVideoSize}),g.trigger(c.RESCALE),"function"==typeof f&&f.call(g)}.call(g),this},refreshImage:function(){return this._scaleImage(),this._options.imagePan&&this.addPan(),this},_preload:function(){if(this._options.preload){var a,b,d,e=this.getNext();try{for(b=this._options.preload;b>0;b--)a=new c.Picture,d=this.getData(e),a.preload(this.isFullscreen()&&d.big?d.big:d.image),e=this.getNext(e)}catch(a){}}},show:function(d,e,f){var g=this._options.swipe;if(g||!(this._queue.length>3||!1===d||!this._options.queue&&this._queue.stalled)){if(d=p.max(0,p.min(parseInt(d,10),this.getDataLength()-1)),e=void 0!==e?!!e:d<this.getIndex(),!(f=f||!1)&&c.History)return void c.History.set(d.toString());if(this.finger&&d!==this._active&&(this.finger.to=-d*this.finger.width,this.finger.index=d),this._active=d,g){var h=this.getData(d),j=this;if(!h)return;var k=this.isFullscreen()&&h.big?h.big:h.image||h.iframe,l=this._controls.slides[d],m=l.isCached(k),n=this._thumbnails[d],o={cached:m,index:d,rewind:e,imageTarget:l.image,thumbTarget:n.image,galleriaData:h};this.trigger(b.extend(o,{type:c.LOADSTART})),j.$("container").removeClass("videoplay");var q=function(){j._layers[d].innerHTML=j.getData().layer||"",j.trigger(b.extend(o,{type:c.LOADFINISH})),j._playCheck()};j._preload(),a.setTimeout(function(){l.ready&&b(l.image).attr("src")==k?(j.trigger(b.extend(o,{type:c.IMAGE})),q()):(h.iframe&&!h.image&&(l.isIframe=!0),l.load(k,function(a){o.imageTarget=a.image,j._scaleImage(a,q).trigger(b.extend(o,{type:c.IMAGE})),q()}))},100)}else i.push.call(this._queue,{index:d,rewind:e}),this._queue.stalled||this._show();return this}},_show:function(){var e=this,f=this._queue[0],g=this.getData(f.index);if(g){var h=this.isFullscreen()&&g.big?g.big:g.image||g.iframe,j=this._controls.getActive(),k=this._controls.getNext(),l=k.isCached(h),m=this._thumbnails[f.index],n=function(){b(k.image).trigger("mouseup")};e.$("container").toggleClass("iframe",!!g.isIframe).removeClass("videoplay");var o=function(f,g,h,j,k){return function(){var l;N.active=!1,L.toggleQuality(g.image,e._options.imageQuality),e._layers[e._controls.active].innerHTML="",b(h.container).css({zIndex:0,opacity:0}).show(),b(h.container).find("iframe, .galleria-videoicon").remove(),b(e._controls.frames[e._controls.active].container).hide(),b(g.container).css({zIndex:1,left:0,top:0}).show(),e._controls.swap(),e._options.imagePan&&e.addPan(g.image),(f.iframe&&f.image||f.link||e._options.lightbox||e._options.clicknext)&&b(g.image).css({cursor:"pointer"}).on("mouseup",function(g){if(!("number"==typeof g.which&&g.which>1)){if(f.iframe){e.isPlaying()&&e.pause();var h=e._controls.frames[e._controls.active],i=e._stageWidth,j=e._stageHeight;return b(h.container).css({width:i,height:j,opacity:0}).show().animate({opacity:1},200),void a.setTimeout(function(){h.load(f.iframe+(f.video?"&autoplay=1":""),{width:i,height:j},function(a){e.$("container").addClass("videoplay"),a.scale({width:e._stageWidth,height:e._stageHeight,iframelimit:f.video?e._options.maxVideoSize:d})})},100)}return e._options.clicknext&&!c.TOUCH?(e._options.pauseOnInteraction&&e.pause(),void e.next()):f.link?void(e._options.popupLinks?l=a.open(f.link,"_blank"):a.location.href=f.link):void(e._options.lightbox&&e.openLightbox())}}),e._playCheck(),e.trigger({type:c.IMAGE,index:j.index,imageTarget:g.image,thumbTarget:k.image,galleriaData:f}),i.shift.call(e._queue),e._queue.stalled=!1,e._queue.length&&e._show()}}(g,k,j,f,m);this._options.carousel&&this._options.carouselFollow&&this._carousel.follow(f.index),e._preload(),L.show(k.container),k.isIframe=g.iframe&&!g.image,b(e._thumbnails[f.index].container).addClass("active").siblings(".active").removeClass("active"),e.trigger({type:c.LOADSTART,cached:l,index:f.index,rewind:f.rewind,imageTarget:k.image,thumbTarget:m.image,galleriaData:g}),e._queue.stalled=!0,k.load(h,function(a){var h=b(e._layers[1-e._controls.active]).html(g.layer||"").hide();e._scaleImage(a,{complete:function(a){"image"in j&&L.toggleQuality(j.image,!1),L.toggleQuality(a.image,!1),e.removePan(),e.setInfo(f.index),e.setCounter(f.index),g.layer&&(h.show(),(g.iframe&&g.image||g.link||e._options.lightbox||e._options.clicknext)&&h.css("cursor","pointer").off("mouseup").mouseup(n)),g.video&&g.image&&M(a.container);var i=e._options.transition;if(b.each({initial:null===j.image,touch:c.TOUCH,fullscreen:e.isFullscreen()},function(a,b){if(b&&e._options[a+"Transition"]!==d)return i=e._options[a+"Transition"],!1}),i in N.effects==!1)o();else{var k={prev:j.container,next:a.container,rewind:f.rewind,speed:e._options.transitionSpeed||400};N.active=!0,N.init.call(e,i,k,o)}e.trigger({type:c.LOADFINISH,cached:l,index:f.index,rewind:f.rewind,imageTarget:a.image,thumbTarget:e._thumbnails[f.index].image,galleriaData:e.getData(f.index)})}})})}},getNext:function(a){return a="number"==typeof a?a:this.getIndex(),a===this.getDataLength()-1?0:a+1},getPrev:function(a){return a="number"==typeof a?a:this.getIndex(),0===a?this.getDataLength()-1:a-1},next:function(){return this.getDataLength()>1&&this.show(this.getNext(),!1),this},prev:function(){return this.getDataLength()>1&&this.show(this.getPrev(),!0),this},get:function(a){return a in this._dom?this._dom[a]:null},getData:function(a){return a in this._data?this._data[a]:this._data[this._active]},getDataLength:function(){return this._data.length},getIndex:function(){return"number"==typeof this._active&&this._active},getStageHeight:function(){return this._stageHeight},getStageWidth:function(){return this._stageWidth},getOptions:function(a){return void 0===a?this._options:this._options[a]},setOptions:function(a,c){return"object"==typeof a?b.extend(this._options,a):this._options[a]=c,this},play:function(a){return this._playing=!0,this._playtime=a||this._playtime,this._playCheck(),this.trigger(c.PLAY),this},pause:function(){return this._playing=!1,this.trigger(c.PAUSE),this},playToggle:function(a){return this._playing?this.pause():this.play(a)},isPlaying:function(){return this._playing},isFullscreen:function(){return this._fullscreen.active},_playCheck:function(){var a=this,b=0,d=L.timestamp(),e="play"+this._id;if(this._playing){this.clearTimer(e);var f=function(){if((b=L.timestamp()-d)>=a._playtime&&a._playing)return a.clearTimer(e),void a.next();a._playing&&(a.trigger({type:c.PROGRESS,percent:p.ceil(b/a._playtime*100),seconds:p.floor(b/1e3),milliseconds:b}),a.addTimer(e,f,20))};a.addTimer(e,f,20)}},setPlaytime:function(a){return this._playtime=a,this},setIndex:function(a){return this._active=a,this},setCounter:function(a){if("number"==typeof a?a++:void 0===a&&(a=this.getIndex()+1),this.get("current").innerHTML=a,t){var b=this.$("counter"),c=b.css("opacity");1===parseInt(c,10)?L.removeAlpha(b[0]):this.$("counter").css("opacity",c)}return this},setInfo:function(a){var c=this,d=this.getData(a);return b.each(["title","description"],function(a,b){var e=c.$("info-"+b);d[b]?e[d[b].length?"show":"hide"]().html(d[b]):e.empty().hide()}),this},hasInfo:function(a){var b,c="title description".split(" ");for(b=0;c[b];b++)if(this.getData(a)[c[b]])return!0;return!1},jQuery:function(a){var c=this,d=[];b.each(a.split(","),function(a,e){e=b.trim(e),c.get(e)&&d.push(e)});var e=b(c.get(d.shift()));return b.each(d,function(a,b){e=e.add(c.get(b))}),e},$:function(a){return this.jQuery.apply(this,L.array(arguments))}},b.each(x,function(a,b){var d=/_/.test(b)?b.replace(/_/g,""):b;c[b.toUpperCase()]="galleria."+d}),b.extend(c,{IE9:9===t,IE8:8===t,IE7:7===t,IE6:6===t,IE:t,WEBKIT:/webkit/.test(m),CHROME:/chrome/.test(m),SAFARI:/safari/.test(m)&&!/chrome/.test(m),QUIRK:t&&e.compatMode&&"BackCompat"===e.compatMode,MAC:/mac/.test(navigator.platform.toLowerCase()),OPERA:!!a.opera,IPHONE:/iphone/.test(m),IPAD:/ipad/.test(m),ANDROID:/android/.test(m),TOUCH:"ontouchstart"in e&&s}),c.addTheme=function(d){d.name||c.raise("No theme name specified"),(!d.version||parseInt(10*c.version)>parseInt(10*d.version))&&c.raise("This version of Galleria requires "+d.name+" theme version "+parseInt(10*c.version)/10+" or later",!0),"object"!=typeof d.defaults?d.defaults={}:d.defaults=y(d.defaults);var e,f,g=!1;return"string"==typeof d.css?(b("link").each(function(a,b){if(e=new RegExp(d.css),e.test(b.href))return g=!0,K(d),!1}),g||b(function(){var h=0,i=function(){b("script").each(function(b,c){e=new RegExp("galleria\\."+d.name.toLowerCase()+"\\."),f=new RegExp("galleria\\.io\\/theme\\/"+d.name.toLowerCase()+"\\/(\\d*\\.*)?(\\d*\\.*)?(\\d*\\/)?js"),(e.test(c.src)||f.test(c.src))&&(g=c.src.replace(/[^\/]*$/,"")+d.css,a.setTimeout(function(){L.loadCSS(g,"galleria-theme-"+d.name,function(){K(d)})},1))}),g||(h++>5?c.raise("No theme CSS loaded"):a.setTimeout(i,500))};i()})):K(d),d},c.loadTheme=function(d,e){if(!b("script").filter(function(){return b(this).attr("src")==d}).length){var f,g=!1;return b(a).on("load",function(){g||(f=a.setTimeout(function(){g||c.raise("Galleria had problems loading theme at "+d+". Please check theme path or load manually.",!0)},2e4))}),L.loadScript(d,function(){g=!0,a.clearTimeout(f)}),c}},c.get=function(a){return F[a]?F[a]:"number"!=typeof a?F:void c.raise("Gallery index "+a+" not found")},c.configure=function(a,d){var e={};return"string"==typeof a&&d?(e[a]=d,a=e):b.extend(e,a),c.configure.options=e,b.each(c.get(),function(a,b){b.setOptions(e)}),c},c.configure.options={},c.on=function(a,d){if(a){d=d||q;var e=a+d.toString().replace(/\s/g,"")+L.timestamp();return b.each(c.get(),function(b,c){c._binds.push(e),c.bind(a,d)}),c.on.binds.push({type:a,callback:d,hash:e}),c}},c.on.binds=[],c.run=function(a,d){return b.isFunction(d)&&(d={extend:d}),b(a||"#galleria").galleria(d),c},c.addTransition=function(a,b){return N.effects[a]=b,c},c.utils=L,c.log=function(){var c=L.array(arguments);if(!("console"in a&&"log"in a.console))return a.alert(c.join("<br>"));try{return a.console.log.apply(a.console,c)}catch(d){b.each(c,function(){a.console.log(this)})}},c.ready=function(a){return"function"!=typeof a?c:(b.each(E,function(b,c){a.call(c,c._options)}),c.ready.callbacks.push(a),c)},c.ready.callbacks=[],c.raise=function(a,c){var d=c?"Fatal error":"Error",e={color:"#fff",position:"absolute",top:0,left:0,zIndex:1e5},f=function(a){var f='<div style="padding:4px;margin:0 0 2px;background:#'+(c?"811":"222")+';">'+(c?"<strong>"+d+": </strong>":"")+a+"</div>";b.each(F,function(){var a=this.$("errors"),b=this.$("target");a.length||(b.css("position","relative"),a=this.addElement("errors").appendChild("target","errors").$("errors").css(e)),a.append(f)}),
F.length||b("<div>").css(b.extend(e,{position:"fixed"})).append(f).appendTo(u().body)};if(j){if(f(a),c)throw new Error(d+": "+a)}else if(c){if(G)return;G=!0,c=!1,f("Gallery could not load.")}},c.version=1.61,c.getLoadedThemes=function(){return b.map(J,function(a){return a.name})},c.requires=function(a,b){return b=b||"You need to upgrade Galleria to version "+a+" to use one or more components.",c.version<a&&c.raise(b,!0),c},c.Picture=function(a){this.id=a||null,this.image=null,this.container=L.create("galleria-image"),b(this.container).css({overflow:"hidden",position:"relative"}),this.original={width:0,height:0},this.ready=!1,this.isIframe=!1},c.Picture.prototype={cache:{},show:function(){L.show(this.image)},hide:function(){L.moveOut(this.image)},clear:function(){this.image=null},isCached:function(a){return!!this.cache[a]},preload:function(a){var c=b(new Image).on("load",function(a,b){return function(){b[a]=a}}(a,this.cache));a.srcset&&c.attr("srcset",a.srcset),a.sizes&&c.attr("sizes",a.sizes),c.attr("src",a)},load:function(d,e,f){if("function"==typeof e&&(f=e,e=null),this.isIframe){var g="if"+(new Date).getTime(),h=this.image=b("<iframe>",{src:d,frameborder:0,id:g,allowfullscreen:!0,css:{visibility:"hidden"}})[0];return e&&b(h).css(e),b(this.container).find("iframe,img").remove(),this.container.appendChild(this.image),b("#"+g).on("load",function(c,d){return function(){a.setTimeout(function(){b(c.image).css("visibility","visible"),"function"==typeof d&&d.call(c,c)},10)}}(this,f)),this.container}this.image=new Image,c.IE8&&b(this.image).css("filter","inherit"),c.IE||c.CHROME||c.SAFARI||b(this.image).css("image-rendering","optimizequality");var i=!1,j=!1,k=b(this.container),m=b(this.image),n=function(){i?l?b(this).attr("src",l):c.raise("Image not found: "+d):(i=!0,a.setTimeout(function(a,b){return function(){a.attr("src",b+(b.indexOf("?")>-1?"&":"?")+L.timestamp())}}(b(this),d),50))},o=function(d,f,g){return function(){var h=function(){b(this).off("load"),d.original=e||{height:this.height,width:this.width},c.HAS3D&&(this.style.MozTransform=this.style.webkitTransform="translate3d(0,0,0)"),k.append(this),d.cache[g]=g,"function"==typeof f&&a.setTimeout(function(){f.call(d,d)},1)};this.width&&this.height?h.call(this):function(a){L.wait({until:function(){return a.width&&a.height},success:function(){h.call(a)},error:function(){j?c.raise("Could not extract width/height from image: "+a.src+". Traced measures: width:"+a.width+"px, height: "+a.height+"px."):(b(new Image).on("load",o).attr("src",a.src),j=!0)},timeout:100})}(this)}}(this,f,d);return k.find("iframe,img").remove(),m.css("display","block"),L.hide(this.image),b.each("minWidth minHeight maxWidth maxHeight".split(" "),function(a,b){m.css(b,/min/.test(b)?"0":"none")}),m.on("load",o).on("error",n),d.srcset&&m.attr("srcset",d.srcset),d.sizes&&m.attr("sizes",d.sizes),m.attr("src",d),this.container},scale:function(a){var e=this;if(a=b.extend({width:0,height:0,min:d,max:d,margin:0,complete:q,position:"center",crop:!1,canvas:!1,iframelimit:d},a),this.isIframe){var f,g,h=a.width,i=a.height;if(a.iframelimit){var j=p.min(a.iframelimit/h,a.iframelimit/i);j<1?(f=h*j,g=i*j,b(this.image).css({top:i/2-g/2,left:h/2-f/2,position:"absolute"})):b(this.image).css({top:0,left:0})}b(this.image).width(f||h).height(g||i).removeAttr("width").removeAttr("height"),b(this.container).width(h).height(i),a.complete.call(e,e);try{this.image.contentWindow&&b(this.image.contentWindow).trigger("resize")}catch(a){}return this.container}if(!this.image)return this.container;var k,l,m,n=b(e.container);return L.wait({until:function(){return k=a.width||n.width()||L.parseValue(n.css("width")),l=a.height||n.height()||L.parseValue(n.css("height")),k&&l},success:function(){var c=(k-2*a.margin)/e.original.width,d=(l-2*a.margin)/e.original.height,f=p.min(c,d),g=p.max(c,d),h={true:g,width:c,height:d,false:f,landscape:e.original.width>e.original.height?g:f,portrait:e.original.width<e.original.height?g:f},i=h[a.crop.toString()],j="";a.max&&(i=p.min(a.max,i)),a.min&&(i=p.max(a.min,i)),b.each(["width","height"],function(a,c){b(e.image)[c](e[c]=e.image[c]=p.round(e.original[c]*i))}),b(e.container).width(k).height(l),a.canvas&&H&&(H.elem.width=e.width,H.elem.height=e.height,j=e.image.src+":"+e.width+"x"+e.height,e.image.src=H.cache[j]||function(a){H.context.drawImage(e.image,0,0,e.original.width*i,e.original.height*i);try{return m=H.elem.toDataURL(),H.length+=m.length,H.cache[a]=m,m}catch(a){return e.image.src}}(j));var n={},o={},q=function(a,c,d){var f=0;if(/\%/.test(a)){var g=parseInt(a,10)/100,h=e.image[c]||b(e.image)[c]();f=p.ceil(-1*h*g+d*g)}else f=L.parseValue(a);return f},r={top:{top:0},left:{left:0},right:{left:"100%"},bottom:{top:"100%"}};b.each(a.position.toLowerCase().split(" "),function(a,b){"center"===b&&(b="50%"),n[a?"top":"left"]=b}),b.each(n,function(a,c){r.hasOwnProperty(c)&&b.extend(o,r[c])}),n=n.top?b.extend(n,o):o,n=b.extend({top:"50%",left:"50%"},n),b(e.image).css({position:"absolute",top:q(n.top,"height",l),left:q(n.left,"width",k)}),e.show(),e.ready=!0,a.complete.call(e,e)},error:function(){c.raise("Could not scale image: "+e.image.src)},timeout:1e3}),this}},b.extend(b.easing,{galleria:function(a,b,c,d,e){return(b/=e/2)<1?d/2*b*b*b+c:d/2*((b-=2)*b*b+2)+c},galleriaIn:function(a,b,c,d,e){return d*(b/=e)*b+c},galleriaOut:function(a,b,c,d,e){return-d*(b/=e)*(b-2)+c}}),c.Finger=function(){var d=(p.abs,c.HAS3D=function(){var a,c,d=e.createElement("p"),f=["webkit","O","ms","Moz",""],g=0,h="transform";for(u().html.insertBefore(d,null);f[g];g++)c=f[g]?f[g]+"Transform":h,void 0!==d.style[c]&&(d.style[c]="translate3d(1px,1px,1px)",a=b(d).css(f[g]?"-"+f[g].toLowerCase()+"-"+h:h));return u().html.removeChild(d),void 0!==a&&a.length>0&&"none"!==a}()),g=function(){var b="RequestAnimationFrame";return a.requestAnimationFrame||a["webkit"+b]||a["moz"+b]||a["o"+b]||a["ms"+b]||function(b){a.setTimeout(b,1e3/60)}}(),h=function(c,e){if(this.config={start:0,duration:500,onchange:function(){},oncomplete:function(){},easing:function(a,b,c,d,e){return-d*((b=b/e-1)*b*b*b-1)+c}},this.easeout=function(a,b,c,d,e){return d*((b=b/e-1)*b*b*b*b+1)+c},c.children.length){var f=this;b.extend(this.config,e),this.elem=c,this.child=c.children[0],this.to=this.pos=0,this.touching=!1,this.start={},this.index=this.config.start,this.anim=0,this.easing=this.config.easing,d||(this.child.style.position="absolute",this.elem.style.position="relative"),b.each(["ontouchstart","ontouchmove","ontouchend","setup"],function(a,b){f[b]=function(a){return function(){a.apply(f,arguments)}}(f[b])}),this.setX=function(){var a=f.child.style;if(!d)return void(a.left=f.pos+"px");a.MozTransform=a.webkitTransform=a.transform="translate3d("+f.pos+"px,0,0)"},b(c).on("touchstart",this.ontouchstart),b(a).on("resize",this.setup),b(a).on("orientationchange",this.setup),this.setup(),function a(){g(a),f.loop.call(f)}()}};return h.prototype={constructor:h,setup:function(){this.width=b(this.elem).width(),this.length=p.ceil(b(this.child).width()/this.width),0!==this.index&&(this.index=p.max(0,p.min(this.index,this.length-1)),this.pos=this.to=-this.width*this.index)},setPosition:function(a){this.pos=a,this.to=a},ontouchstart:function(a){var b=a.originalEvent.touches;this.start={pageX:b[0].pageX,pageY:b[0].pageY,time:+new Date},this.isScrolling=null,this.touching=!0,this.deltaX=0,f.on("touchmove",this.ontouchmove),f.on("touchend",this.ontouchend)},ontouchmove:function(a){var b=a.originalEvent.touches;b&&b.length>1||a.scale&&1!==a.scale||(this.deltaX=b[0].pageX-this.start.pageX,null===this.isScrolling&&(this.isScrolling=!!(this.isScrolling||p.abs(this.deltaX)<p.abs(b[0].pageY-this.start.pageY))),this.isScrolling||(a.preventDefault(),this.deltaX/=!this.index&&this.deltaX>0||this.index==this.length-1&&this.deltaX<0?p.abs(this.deltaX)/this.width+1.8:1,this.to=this.deltaX-this.index*this.width),a.stopPropagation())},ontouchend:function(a){this.touching=!1;var b=+new Date-this.start.time<250&&p.abs(this.deltaX)>40||p.abs(this.deltaX)>this.width/2,c=!this.index&&this.deltaX>0||this.index==this.length-1&&this.deltaX<0;this.isScrolling||this.show(this.index+(b&&!c?this.deltaX<0?1:-1:0)),f.off("touchmove",this.ontouchmove),f.off("touchend",this.ontouchend)},show:function(a){a!=this.index?this.config.onchange.call(this,a):this.to=-a*this.width},moveTo:function(a){a!=this.index&&(this.pos=this.to=-a*this.width,this.index=a)},loop:function(){var a=this.to-this.pos,b=1;if(this.width&&a&&(b=p.max(.5,p.min(1.5,p.abs(a/this.width)))),this.touching||p.abs(a)<=1)this.pos=this.to,a=0,this.anim&&!this.touching&&this.config.oncomplete(this.index),this.anim=0,this.easing=this.config.easing;else{this.anim||(this.anim={start:this.pos,time:+new Date,distance:a,factor:b,destination:this.to});var c=+new Date-this.anim.time,d=this.config.duration*this.anim.factor;if(c>d||this.anim.destination!=this.to)return this.anim=0,void(this.easing=this.easeout);this.pos=this.easing(null,c,this.anim.start,this.anim.distance,d)}this.setX()}},h}(),b.fn.galleria=function(a){var d=this.selector;return b(this).length?this.each(function(){b.data(this,"galleria")&&(b.data(this,"galleria").destroy(),b(this).find("*").hide()),b.data(this,"galleria",(new c).init(this,a))}):(b(function(){b(d).length?b(d).galleria(a):c.utils.wait({until:function(){return b(d).length},success:function(){b(d).galleria(a)},error:function(){c.raise('Init failed: Galleria could not find the element "'+d+'".')},timeout:5e3})}),this)},c});