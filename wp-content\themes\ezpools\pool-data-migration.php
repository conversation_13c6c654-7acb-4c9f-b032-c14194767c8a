<?php
/**
 * Pool Data Migration Script
 * Creates pool products from predefined data and associates with copied images
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class EZPoolDataMigration {

    private $pools_data = array();
    private $image_base_path;

    public function __construct() {
        $upload_dir = wp_upload_dir();
        $this->image_base_path = $upload_dir['basedir'] . '/pool-products/';
        $this->init_pool_data();
    }
    
    /**
     * Main migration function
     */
    public function migrate_all_pools() {
        echo "Starting pool data migration...\n";

        // Step 1: Extract data from HTML files
        $this->extract_pool_data();

        // Step 2: Copy images
        $this->copy_pool_images();

        // Step 3: Create WordPress posts
        $this->create_wordpress_posts();

        echo "Migration completed!\n";
    }

    /**
     * Extract pool data from HTML files
     */
    private function extract_pool_data() {
        echo "Extracting pool data from HTML files...\n";

        $html_files = glob($this->old_website_path . '*.html');

        foreach ($html_files as $file) {
            $filename = basename($file, '.html');

            // Check if it's a pool size file (format: 7x12, 12x17, etc.)
            if (preg_match('/^(\d+)x(\d+)$/', $filename, $matches)) {
                $width = intval($matches[1]);
                $length = intval($matches[2]);

                echo "Processing pool: {$width}' x {$length}'...\n";

                $pool_data = $this->parse_pool_html($file, $width, $length);
                if ($pool_data) {
                    $this->pools_data[] = $pool_data;
                }
            }
        }

        echo "Extracted data for " . count($this->pools_data) . " pools.\n";
    }

    /**
     * Parse individual pool HTML file
     */
    private function parse_pool_html($file_path, $width, $length) {
        $content = file_get_contents($file_path);
        if (!$content) {
            return false;
        }

        $pool_data = array(
            'width' => $width,
            'length' => $length,
            'title' => "{$width}' x {$length}' EZ Pool",
            'slug' => "{$width}x{$length}",
            'accessories' => array(),
            'images' => array(),
            'base_price' => 0,
            'shipping_cost' => 450,
            'gallons' => 0,
            'depth' => '52"',
            'footprint' => '',
            'top_dimensions' => '',
            'category' => $this->determine_category($width)
        );

        // Extract title from HTML
        if (preg_match('/<title>(.*?)<\/title>/i', $content, $matches)) {
            $pool_data['title'] = html_entity_decode(strip_tags($matches[1]));
        }

        // Extract base pool price
        if (preg_match('/name="item_name"\s+value="' . $width . '\'\s*x\s*' . $length . '\'\s*EZ\s*Pool".*?name="amount"\s+value="(\d+)"/s', $content, $matches)) {
            $pool_data['base_price'] = intval($matches[1]);
        }

        // Extract accessories with prices
        $this->extract_accessories($content, $pool_data);

        // Calculate estimated gallons (rough formula)
        $pool_data['gallons'] = round($width * $length * 4.33 * 0.8); // 52" depth, 80% fill

        // Set footprint and top dimensions
        $pool_data['footprint'] = ($width + 2) . "' x " . ($length + 2) . "'";
        $pool_data['top_dimensions'] = ($width - 1) . "' x " . ($length - 1) . "' x 52\"";

        // Find images for this pool size
        $pool_data['images'] = $this->find_pool_images($width, $length);

        return $pool_data;
    }

    /**
     * Determine pool category based on width
     */
    private function determine_category($width) {
        if ($width <= 7) return '7-wide-lap';
        if ($width <= 12) return '12-wide-family';
        if ($width <= 17) return '17-wide-super';
        return '22-wide-giant';
    }

    /**
     * Extract accessories from HTML content
     */
    private function extract_accessories($content, &$pool_data) {
        // Common accessories patterns
        $accessory_patterns = array(
            'Extra (\d+) Sq Ft Cartridge' => 'Extra {1} Sq Ft Cartridge',
            '4-Way Outdoor Timer' => '4-Way Outdoor Timer',
            'Pool Step' => 'Pool Step',
            'EZ Pool Care Kit' => 'EZ Pool Care Kit',
            'Extra Flo-Kit' => 'Extra Flo-Kit',
            'Solar Cover' => 'Solar Cover',
            'Pool Cover to Fit Size' => 'Pool Cover to Fit Size',
            'EZ SwimMill The Swimmer\'s Treadmill' => 'EZ SwimMill',
            '(\d+)HP Pump (\d+) Sq Ft Filter' => '{1}HP Pump {2} Sq Ft Filter',
            'Add White RimGard' => 'White RimGard',
            'Add (\d+) Inch Wide Black Swimmer\'s Lane' => '{1} Inch Wide Black Swimmer\'s Lane',
            'A-Frame Ldder' => 'A-Frame Ladder',
            'InPool Skimmer' => 'In-Pool Skimmer',
            'Add Staycation Package' => 'Staycation Package'
        );

        foreach ($accessory_patterns as $pattern => $name) {
            if (preg_match_all('/name="item_name"\s+value="' . str_replace('\\', '\\\\', $pattern) . '".*?name="amount"\s+value="(\d+)"/s', $content, $matches, PREG_SET_ORDER)) {
                foreach ($matches as $match) {
                    $accessory_name = $name;
                    // Replace placeholders with captured groups
                    for ($i = 1; $i < count($match) - 1; $i++) {
                        $accessory_name = str_replace('{' . $i . '}', $match[$i], $accessory_name);
                    }

                    $pool_data['accessories'][] = array(
                        'name' => $accessory_name,
                        'price' => intval($match[count($match) - 1]),
                        'description' => $this->get_accessory_description($accessory_name)
                    );
                }
            }
        }
    }

    /**
     * Initialize pool product data
     */
    private function init_pool_data() {
        // Define all pool sizes with their specifications
        $pool_specs = array(
            // 7-Wide Lap Pools
            array('width' => 7, 'length' => 12, 'price' => 1650, 'gallons' => 2500, 'category' => '7-wide-lap'),
            array('width' => 7, 'length' => 17, 'price' => 2100, 'gallons' => 3500, 'category' => '7-wide-lap'),
            array('width' => 7, 'length' => 22, 'price' => 2550, 'gallons' => 4500, 'category' => '7-wide-lap'),
            array('width' => 7, 'length' => 27, 'price' => 3000, 'gallons' => 5500, 'category' => '7-wide-lap'),
            array('width' => 7, 'length' => 32, 'price' => 3450, 'gallons' => 6500, 'category' => '7-wide-lap'),
            array('width' => 7, 'length' => 37, 'price' => 3900, 'gallons' => 7500, 'category' => '7-wide-lap'),
            array('width' => 7, 'length' => 42, 'price' => 4350, 'gallons' => 8500, 'category' => '7-wide-lap'),
            array('width' => 7, 'length' => 47, 'price' => 4800, 'gallons' => 9500, 'category' => '7-wide-lap'),

            // 12-Wide Family Pools
            array('width' => 12, 'length' => 12, 'price' => 2400, 'gallons' => 5000, 'category' => '12-wide-family'),
            array('width' => 12, 'length' => 17, 'price' => 3000, 'gallons' => 7000, 'category' => '12-wide-family'),
            array('width' => 12, 'length' => 22, 'price' => 3600, 'gallons' => 9000, 'category' => '12-wide-family'),
            array('width' => 12, 'length' => 27, 'price' => 4200, 'gallons' => 11000, 'category' => '12-wide-family'),
            array('width' => 12, 'length' => 32, 'price' => 4800, 'gallons' => 13000, 'category' => '12-wide-family'),
            array('width' => 12, 'length' => 37, 'price' => 5400, 'gallons' => 15000, 'category' => '12-wide-family'),
            array('width' => 12, 'length' => 42, 'price' => 6000, 'gallons' => 17000, 'category' => '12-wide-family'),
            array('width' => 12, 'length' => 47, 'price' => 6600, 'gallons' => 19000, 'category' => '12-wide-family'),

            // 17-Wide Super Pools
            array('width' => 17, 'length' => 17, 'price' => 4200, 'gallons' => 12000, 'category' => '17-wide-super'),
            array('width' => 17, 'length' => 22, 'price' => 5000, 'gallons' => 15000, 'category' => '17-wide-super'),
            array('width' => 17, 'length' => 27, 'price' => 5800, 'gallons' => 18000, 'category' => '17-wide-super'),
            array('width' => 17, 'length' => 32, 'price' => 6600, 'gallons' => 21000, 'category' => '17-wide-super'),
            array('width' => 17, 'length' => 37, 'price' => 7400, 'gallons' => 24000, 'category' => '17-wide-super'),
            array('width' => 17, 'length' => 42, 'price' => 8200, 'gallons' => 27000, 'category' => '17-wide-super'),
            array('width' => 17, 'length' => 47, 'price' => 9000, 'gallons' => 30000, 'category' => '17-wide-super'),

            // 22-Wide Giant Pools
            array('width' => 22, 'length' => 22, 'price' => 6500, 'gallons' => 20000, 'category' => '22-wide-giant'),
            array('width' => 22, 'length' => 27, 'price' => 7200, 'gallons' => 24000, 'category' => '22-wide-giant'),
            array('width' => 22, 'length' => 32, 'price' => 7900, 'gallons' => 28000, 'category' => '22-wide-giant'),
            array('width' => 22, 'length' => 37, 'price' => 8600, 'gallons' => 32000, 'category' => '22-wide-giant'),
            array('width' => 22, 'length' => 42, 'price' => 9300, 'gallons' => 36000, 'category' => '22-wide-giant'),
            array('width' => 22, 'length' => 47, 'price' => 10000, 'gallons' => 40000, 'category' => '22-wide-giant'),
            array('width' => 22, 'length' => 52, 'price' => 10700, 'gallons' => 44000, 'category' => '22-wide-giant'),
        );

        // Convert specs to full pool data
        foreach ($pool_specs as $spec) {
            $this->pools_data[] = array(
                'title' => $spec['width'] . "' x " . $spec['length'] . "' EZ Pool",
                'width' => $spec['width'],
                'length' => $spec['length'],
                'depth' => '52"',
                'base_price' => $spec['price'],
                'shipping_cost' => 450,
                'gallons' => $spec['gallons'],
                'footprint' => ($spec['width'] + 2) . "' x " . ($spec['length'] + 2) . "'",
                'top_dimensions' => ($spec['width'] - 1) . "' x " . ($spec['length'] - 1) . "' x 52\"",
                'category' => $spec['category'],
                'slug' => $spec['width'] . 'x' . $spec['length'],
                'description' => $this->get_pool_description($spec['width'], $spec['length'], $spec['category'])
            );
        }
    }

    /**
     * Get description based on pool size and category
     */
    private function get_pool_description($width, $length, $category) {
        $descriptions = array(
            '7-wide-lap' => 'Perfect for lap swimming and exercise. Compact design fits in most backyards.',
            '12-wide-family' => 'Spacious family pool with room for swimming and recreational activities.',
            '17-wide-super' => 'Super-sized pool for serious swimmers and large families.',
            '22-wide-giant' => 'Giant pool for ultimate backyard luxury and commercial use.'
        );

        return $descriptions[$category] ?? 'High-quality portable pool made in the USA.';
    }

    /**
     * Run the migration
     */
    public function run_migration() {
        $results = array(
            'pools_created' => 0,
            'categories_created' => 0,
            'errors' => array()
        );

        try {
            // Create pool categories first
            $this->create_pool_categories();
            $results['categories_created'] = 4;

            // Create pool products
            foreach ($this->pools_data as $pool) {
                if ($this->create_pool_product($pool)) {
                    $results['pools_created']++;
                } else {
                    $results['errors'][] = 'Failed to create pool: ' . $pool['title'];
                }
            }

        } catch (Exception $e) {
            $results['errors'][] = 'Migration error: ' . $e->getMessage();
        }

        return $results;
    }

    /**
     * Create pool categories
     */
    private function create_pool_categories() {
        $categories = array(
            array('name' => '7\' Wide Lap Pools', 'slug' => '7-wide-lap'),
            array('name' => '12\' Wide Family Pools', 'slug' => '12-wide-family'),
            array('name' => '17\' Wide Super Pools', 'slug' => '17-wide-super'),
            array('name' => '22\' Wide Giant Pools', 'slug' => '22-wide-giant'),
        );

        foreach ($categories as $category) {
            if (!term_exists($category['slug'], 'pool_category')) {
                wp_insert_term($category['name'], 'pool_category', array(
                    'slug' => $category['slug']
                ));
            }
        }
    }

    /**
     * Create a pool product
     */
    private function create_pool_product($pool_data) {
        // Debug output
        if (defined('WP_CLI') || (defined('DOING_CRON') && DOING_CRON) || php_sapi_name() === 'cli') {
            echo "Creating pool: {$pool_data['title']}\n";
        }

        // Check if product already exists
        $existing = get_page_by_title($pool_data['title'], OBJECT, 'pool_product');
        if ($existing) {
            if (defined('WP_CLI') || (defined('DOING_CRON') && DOING_CRON) || php_sapi_name() === 'cli') {
                echo "  Pool already exists, skipping\n";
            }
            return false; // Already exists
        }

        // Create the post
        $post_data = array(
            'post_title' => $pool_data['title'],
            'post_content' => $this->generate_pool_description($pool_data),
            'post_status' => 'publish',
            'post_type' => 'pool_product',
            'post_excerpt' => $pool_data['description']
        );

        $post_id = wp_insert_post($post_data);

        if ($post_id && !is_wp_error($post_id)) {
            if (defined('WP_CLI') || (defined('DOING_CRON') && DOING_CRON) || php_sapi_name() === 'cli') {
                echo "  Created pool with ID: $post_id\n";
            }

            // Add meta fields
            update_post_meta($post_id, '_pool_width', $pool_data['width']);
            update_post_meta($post_id, '_pool_length_min', $pool_data['length']);
            update_post_meta($post_id, '_pool_depth', $pool_data['depth']);
            update_post_meta($post_id, '_pool_base_price', $pool_data['base_price']);
            update_post_meta($post_id, '_pool_shipping_cost', $pool_data['shipping_cost']);
            update_post_meta($post_id, '_pool_gallons', $pool_data['gallons']);
            update_post_meta($post_id, '_pool_footprint', $pool_data['footprint']);
            update_post_meta($post_id, '_pool_top_dimensions', $pool_data['top_dimensions']);
            update_post_meta($post_id, '_pool_price_range', 'Starting at $' . number_format($pool_data['base_price']));

            // Assign category
            wp_set_object_terms($post_id, $pool_data['category'], 'pool_category');

            // Set featured image
            $this->set_featured_image($post_id, $pool_data);

            return true;
        } else {
            if (defined('WP_CLI') || (defined('DOING_CRON') && DOING_CRON) || php_sapi_name() === 'cli') {
                if (is_wp_error($post_id)) {
                    echo "  ERROR creating pool: " . $post_id->get_error_message() . "\n";
                } else {
                    echo "  ERROR: wp_insert_post returned false\n";
                }
            }
        }

        return false;
    }

    /**
     * Set featured image for pool
     */
    private function set_featured_image($post_id, $pool_data) {
        $image_dir = $this->image_base_path . $pool_data['slug'] . '/';

        if (defined('WP_CLI') || (defined('DOING_CRON') && DOING_CRON) || php_sapi_name() === 'cli') {
            echo "  Looking for images in: $image_dir\n";
        }

        // Look for the standardized image: {width}x{length}@2x.{extension}
        $standardized_name = $pool_data['slug'] . '@2x';
        $image_extensions = array('jpg', 'png', 'jpeg');

        if (is_dir($image_dir)) {
            foreach ($image_extensions as $ext) {
                $image_filename = $standardized_name . '.' . $ext;
                $image_path = $image_dir . $image_filename;

                if (file_exists($image_path)) {
                    if (defined('WP_CLI') || (defined('DOING_CRON') && DOING_CRON) || php_sapi_name() === 'cli') {
                        echo "  Found standardized image: $image_filename\n";
                    }

                    $attachment_id = $this->create_attachment($image_path, $post_id, $pool_data['title']);
                    if ($attachment_id) {
                        set_post_thumbnail($post_id, $attachment_id);
                        if (defined('WP_CLI') || (defined('DOING_CRON') && DOING_CRON) || php_sapi_name() === 'cli') {
                            echo "  Set featured image: $image_filename (attachment ID: $attachment_id)\n";
                        }
                        return; // Successfully set featured image
                    }
                }
            }

            if (defined('WP_CLI') || (defined('DOING_CRON') && DOING_CRON) || php_sapi_name() === 'cli') {
                echo "  No standardized image found for pattern: $standardized_name.{jpg|png|jpeg}\n";
            }
        } else {
            if (defined('WP_CLI') || (defined('DOING_CRON') && DOING_CRON) || php_sapi_name() === 'cli') {
                echo "  Image directory does not exist\n";
            }
        }

        // If no standardized image found, pool will have no featured image
        if (defined('WP_CLI') || (defined('DOING_CRON') && DOING_CRON) || php_sapi_name() === 'cli') {
            echo "  No featured image set for this pool\n";
        }
    }

    /**
     * Create WordPress attachment from image file
     */
    private function create_attachment($file_path, $post_id, $title) {
        $filename = basename($file_path);
        $upload_dir = wp_upload_dir();

        // Copy to uploads directory if not already there
        $new_file = $upload_dir['path'] . '/' . $filename;
        if (!file_exists($new_file)) {
            copy($file_path, $new_file);
        }

        $attachment = array(
            'guid' => $upload_dir['url'] . '/' . $filename,
            'post_mime_type' => wp_check_filetype($filename)['type'],
            'post_title' => $title . ' - ' . preg_replace('/\.[^.]+$/', '', $filename),
            'post_content' => '',
            'post_status' => 'inherit'
        );

        $attachment_id = wp_insert_attachment($attachment, $new_file, $post_id);

        if ($attachment_id) {
            require_once(ABSPATH . 'wp-admin/includes/image.php');
            $attachment_data = wp_generate_attachment_metadata($attachment_id, $new_file);
            wp_update_attachment_metadata($attachment_id, $attachment_data);
        }

        return $attachment_id;
    }

    /**
     * Generate pool description
     */
    private function generate_pool_description($pool_data) {
        $description = "<p>{$pool_data['description']}</p>";

        $description .= "<h3>Specifications</h3>";
        $description .= "<ul>";
        $description .= "<li><strong>Size:</strong> {$pool_data['width']}' x {$pool_data['length']}'</li>";
        $description .= "<li><strong>Depth:</strong> {$pool_data['depth']}</li>";
        $description .= "<li><strong>Capacity:</strong> " . number_format($pool_data['gallons']) . " gallons</li>";
        $description .= "<li><strong>Footprint:</strong> {$pool_data['footprint']}</li>";
        $description .= "<li><strong>Top Dimensions:</strong> {$pool_data['top_dimensions']}</li>";
        $description .= "</ul>";

        $description .= "<h3>Features</h3>";
        $description .= "<ul>";
        $description .= "<li>Made-to-Order in the USA</li>";
        $description .= "<li>5-Year Warranty</li>";
        $description .= "<li>American-Made Components</li>";
        $description .= "<li>Includes Flo-Kit Set</li>";
        $description .= "<li>Ships in 10-14 Business Days</li>";
        $description .= "</ul>";

        return $description;
    }
}

// Admin interface for running migration
if (is_admin()) {
    add_action('admin_menu', function() {
        add_submenu_page(
            'edit.php?post_type=pool_product',
            'Import Pool Data',
            'Import Data',
            'manage_options',
            'pool-data-migration',
            'ezpools_migration_page'
        );
    });
}

function ezpools_migration_page() {
    if (isset($_POST['run_migration']) && wp_verify_nonce($_POST['migration_nonce'], 'run_pool_migration')) {
        $migration = new EZPoolDataMigration();
        $results = $migration->run_migration();

        echo '<div class="notice notice-success"><p>';
        echo "Migration completed! ";
        echo "Created {$results['pools_created']} pools and ";
        echo "{$results['categories_created']} categories.";
        if (!empty($results['errors'])) {
            echo "<br>Errors: " . implode(', ', $results['errors']);
        }
        echo '</p></div>';
    }

    ?>
    <div class="wrap">
        <h1>Pool Data Migration</h1>
        <p>This will import pool products with images from the copied pool data.</p>

        <form method="post">
            <?php wp_nonce_field('run_pool_migration', 'migration_nonce'); ?>
            <p>
                <input type="submit" name="run_migration" class="button button-primary" value="Run Migration"
                       onclick="return confirm('This will create new pool products. Continue?');">
            </p>
        </form>

        <h2>What will be imported:</h2>
        <ul>
            <li>4 Pool Categories (7' Wide, 12' Wide, 17' Wide, 22' Wide)</li>
            <li>30+ Pool Products with specifications, pricing, and images</li>
            <li>Featured images from copied pool product images</li>
        </ul>
    </div>
    <?php
}
?>
