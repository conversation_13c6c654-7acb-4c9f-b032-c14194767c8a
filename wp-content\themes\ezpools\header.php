<!DOCTYPE html>
<html <?php language_attributes(); ?>>
<head>
    <meta charset="<?php bloginfo('charset'); ?>">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title><?php wp_title('|', true, 'right'); ?><?php bloginfo('name'); ?></title>
    <meta name="description" content="EZ Pools - The Better Portable Pool. Custom portable pools, lap swimming, event pools, dog pools, and more. Made-to-order quality pools at 1/10th the cost of traditional pools.">

    <!-- Development Cache Buster - Remove in Production -->
    <meta name="cache-buster" content="<?php echo microtime(true); ?>">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate, max-age=0">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="Thu, 01 Jan 1970 00:00:00 GMT">

    <script>
    // Force browser to reload resources on every page load
    if (window.performance && window.performance.navigation.type !== 1) {
        // Not a refresh, force reload to get latest changes
        setTimeout(function() {
            if (document.readyState === 'complete') {
                console.log('🔄 Development mode: Forcing resource refresh');
            }
        }, 100);
    }

    // Add timestamp to all resource URLs to prevent caching
    document.addEventListener('DOMContentLoaded', function() {
        var timestamp = new Date().getTime();
        var links = document.querySelectorAll('link[rel="stylesheet"]');
        links.forEach(function(link) {
            var href = link.href;
            if (href.indexOf('?') > -1) {
                link.href = href + '&t=' + timestamp;
            } else {
                link.href = href + '?t=' + timestamp;
            }
        });
    });
    </script>

    <?php wp_head(); ?>
</head>
<body <?php body_class(); ?>>

<!-- Header -->
<header class="site-header">
    <div class="container">
        <div class="header-content">
            <!-- Sales Guy Logo -->
            <div class="sales-guy-logo">
                <img src="<?php echo get_template_directory_uri(); ?>/images/salesguy-45.png" alt="EZ Pools Sales Guy" class="salesguy-icon">
            </div>

            <?php if (has_custom_logo()) : ?>
                <div class="site-logo">
                    <?php the_custom_logo(); ?>
                </div>
            <?php else : ?>
                <a href="<?php echo home_url(); ?>" class="logo">
                    <?php bloginfo('name'); ?>
                </a>
            <?php endif; ?>

            <nav class="main-nav">
                <?php
                wp_nav_menu(array(
                    'theme_location' => 'primary',
                    'menu_class' => 'nav-menu',
                    'container' => false,
                    'fallback_cb' => 'ezpools_fallback_menu',
                ));
                ?>
            </nav>
            
            <button class="mobile-menu-toggle" aria-label="Toggle mobile menu">
                <span></span>
                <span></span>
                <span></span>
            </button>
        </div>
    </div>
</header>

<?php
// Fallback menu if no menu is assigned
function ezpools_fallback_menu() {
    echo '<ul class="nav-menu">';
    echo '<li><a href="' . home_url() . '">Home</a></li>';
    echo '<li><a href="' . home_url('/pools/') . '">Products</a></li>';
    echo '<li><a href="' . home_url('/#about') . '">About</a></li>';
    echo '<li><a href="' . home_url('/#contact') . '">Contact</a></li>';
    echo '</ul>';
}
?>
