[mysqld]
# Allow connections from any host in the Docker network
bind-address = 0.0.0.0

# Disable SSL for Docker development environment
skip-ssl
ssl = 0

# Increase connection limits
max_connections = 200

# Set default authentication plugin for compatibility
default-authentication-plugin = mysql_native_password

# Character set configuration
character-set-server = utf8mb4
collation-server = utf8mb4_unicode_ci

# Performance tuning for development
innodb_buffer_pool_size = 256M
innodb_log_file_size = 64M
innodb_flush_log_at_trx_commit = 2
innodb_flush_method = O_DIRECT

# Logging
general_log = 0
slow_query_log = 1
slow_query_log_file = /var/log/mysql/slow.log
long_query_time = 2

# Skip DNS resolution for faster connections
skip-name-resolve = 1
