#!/bin/bash
# EZ Pools WordPress Auto-Setup Script
# This script sets up the EZ Pools theme, content, and configuration

set -e

# Force MySQL client to not use SSL
export MYSQL_CLIENT_FLAGS=0

echo "🏊‍♂️ Running EZ Pools auto-setup..."

# Set WordPress path
WP_PATH="/var/www/html"
WP_CLI="wp --allow-root --path=$WP_PATH"

# Wait for WordPress files to be ready
echo "⏳ Waiting for WordPress files to be ready..."
while [ ! -f "$WP_PATH/wp-config.php" ]; do
    echo "   Waiting for WordPress files..."
    sleep 2
done

# Wait for database to be ready
echo "⏳ Waiting for database connection..."
while ! mariadb -h db -u ezpools_user -pezpools_password --ssl=0 -e "SELECT 1" 2>/dev/null; do
    echo "   Waiting for database..."
    sleep 2
done

# Check if WordPress is already installed
if $WP_CLI core is-installed 2>/dev/null; then
    echo "✅ WordPress is already installed!"
else
    echo "📦 Installing WordPress..."
    $WP_CLI core install \
        --url="http://localhost:8080" \
        --title="EZ Pools - The Better Portable Pool" \
        --admin_user="admin" \
        --admin_password="admin123" \
        --admin_email="<EMAIL>" \
        --skip-email 2>/dev/null || {
        echo "❌ WordPress installation failed, but continuing..."
    }
fi

echo "✅ WordPress is ready!"

# Activate EZ Pools theme
if $WP_CLI theme is-installed ezpools 2>/dev/null; then
    echo "🎨 Activating EZ Pools theme..."
    if $WP_CLI theme activate ezpools 2>/dev/null; then
        echo "✅ EZ Pools theme activated!"
    else
        echo "⚠️ Failed to activate EZ Pools theme, using default theme"
    fi
else
    echo "⚠️ EZ Pools theme not found, using default theme"
fi

# Create sample pages
echo "📄 Creating sample pages..."

# Home page (will be set as front page)
if ! $WP_CLI post list --post_type=page --name=home --format=count 2>/dev/null | grep -q "1"; then
    HOME_PAGE_ID=$($WP_CLI post create --post_type=page --post_title="Home" --post_name="home" --post_status=publish --post_content="This is the homepage content. It will be overridden by front-page.php template." --porcelain 2>/dev/null || echo "")
    if [ ! -z "$HOME_PAGE_ID" ]; then
        echo "✅ Created Home page (ID: $HOME_PAGE_ID)"
    else
        echo "⚠️ Failed to create Home page"
        HOME_PAGE_ID=""
    fi
else
    HOME_PAGE_ID=$($WP_CLI post list --post_type=page --name=home --field=ID --format=csv 2>/dev/null || echo "")
    echo "ℹ️  Home page already exists (ID: $HOME_PAGE_ID)"
fi

# About page
if ! $WP_CLI post list --post_type=page --name=about --format=count 2>/dev/null | grep -q "1"; then
    if $WP_CLI post create --post_type=page --post_title="About EZ Pools" --post_name="about" --post_status=publish --post_content="About EZ Pools - 30 years of pool innovation. Custom portable pools made-to-order. From carton to completion in less than one hour. All the features of traditional pools at 1/10th the cost." 2>/dev/null; then
        echo "✅ Created About page"
    else
        echo "⚠️ Failed to create About page"
    fi
else
    echo "ℹ️  About page already exists"
fi

# Contact page
if ! $WP_CLI post list --post_type=page --name=contact --format=count 2>/dev/null | grep -q "1"; then
    if $WP_CLI post create --post_type=page --post_title="Contact Us" --post_name="contact" --post_status=publish --post_content="Contact EZ Pools for your free quote. Phone: (855) 4EZ-POOL. Email: <EMAIL>. Hours: Monday-Friday 8AM-6PM EST." 2>/dev/null; then
        echo "✅ Created Contact page"
    else
        echo "⚠️ Failed to create Contact page"
    fi
else
    echo "ℹ️  Contact page already exists"
fi

# Set front page
if [ ! -z "$HOME_PAGE_ID" ]; then
    echo "🏠 Setting front page..."
    if $WP_CLI option update show_on_front page 2>/dev/null && $WP_CLI option update page_on_front $HOME_PAGE_ID 2>/dev/null; then
        echo "✅ Set Home as front page"
    else
        echo "⚠️ Failed to set front page"
    fi
else
    echo "⚠️ Skipping front page setup (no Home page ID)"
fi

# Create sample pool products
echo "🏊‍♂️ Creating sample pool products..."

# 7x12 Lap Pool
if ! $WP_CLI post list --post_type=pool_product --s="7x12 Lap Pool" --format=count 2>/dev/null | grep -q "1"; then
    POOL_ID=$($WP_CLI post create --post_type=pool_product --post_title="7x12 Lap Pool" --post_status=publish --post_content="Perfect starter lap pool for fitness enthusiasts. Compact design fits in smaller backyards while providing excellent swimming experience." --post_excerpt="Compact lap pool perfect for fitness and smaller spaces. Quick assembly, durable construction." --porcelain 2>/dev/null || echo "")

    if [ ! -z "$POOL_ID" ]; then
        $WP_CLI post meta update $POOL_ID _pool_width "7" 2>/dev/null || true
        $WP_CLI post meta update $POOL_ID _pool_length_min "12" 2>/dev/null || true
        $WP_CLI post meta update $POOL_ID _pool_length_max "12" 2>/dev/null || true
        $WP_CLI post meta update $POOL_ID _pool_depth "4" 2>/dev/null || true
        $WP_CLI post meta update $POOL_ID _pool_price_range "\$3,500 - \$4,500" 2>/dev/null || true
        echo "✅ Created 7x12 Lap Pool"
    else
        echo "⚠️ Failed to create 7x12 Lap Pool"
    fi
else
    echo "ℹ️  7x12 Lap Pool already exists"
fi

# 12x17 Family Pool
if ! $WP_CLI post list --post_type=pool_product --s="12x17 Family Pool" --format=count 2>/dev/null | grep -q "1"; then
    POOL_ID=$($WP_CLI post create --post_type=pool_product --post_title="12x17 Family Pool" --post_status=publish --post_content="Our most popular family pool size. Perfect balance of swimming space and backyard fit. Great for families with children." --post_excerpt="Most popular family pool size. Perfect for recreation and family fun." --porcelain 2>/dev/null || echo "")

    if [ ! -z "$POOL_ID" ]; then
        $WP_CLI post meta update $POOL_ID _pool_width "12" 2>/dev/null || true
        $WP_CLI post meta update $POOL_ID _pool_length_min "17" 2>/dev/null || true
        $WP_CLI post meta update $POOL_ID _pool_length_max "17" 2>/dev/null || true
        $WP_CLI post meta update $POOL_ID _pool_depth "4" 2>/dev/null || true
        $WP_CLI post meta update $POOL_ID _pool_price_range "\$6,500 - \$8,500" 2>/dev/null || true
        echo "✅ Created 12x17 Family Pool"
    else
        echo "⚠️ Failed to create 12x17 Family Pool"
    fi
else
    echo "ℹ️  12x17 Family Pool already exists"
fi

# 22x32 Giant Pool
if ! $WP_CLI post list --post_type=pool_product --s="22x32 Giant Pool" --format=count 2>/dev/null | grep -q "1"; then
    POOL_ID=$($WP_CLI post create --post_type=pool_product --post_title="22x32 Giant Pool" --post_status=publish --post_content="Our largest standard pool for maximum swimming space. Perfect for large families, events, or commercial use." --post_excerpt="Our largest standard pool for maximum space and commercial applications." --porcelain 2>/dev/null || echo "")

    if [ ! -z "$POOL_ID" ]; then
        $WP_CLI post meta update $POOL_ID _pool_width "22" 2>/dev/null || true
        $WP_CLI post meta update $POOL_ID _pool_length_min "32" 2>/dev/null || true
        $WP_CLI post meta update $POOL_ID _pool_length_max "32" 2>/dev/null || true
        $WP_CLI post meta update $POOL_ID _pool_depth "4-6" 2>/dev/null || true
        $WP_CLI post meta update $POOL_ID _pool_price_range "\$15,000 - \$20,000" 2>/dev/null || true
        echo "✅ Created 22x32 Giant Pool"
    else
        echo "⚠️ Failed to create 22x32 Giant Pool"
    fi
else
    echo "ℹ️  22x32 Giant Pool already exists"
fi

# Create sample testimonials
echo "💬 Creating sample testimonials..."

if ! $WP_CLI post list --post_type=testimonial --s="Amazing Pool Experience" --format=count 2>/dev/null | grep -q "1"; then
    if $WP_CLI post create --post_type=testimonial --post_title="Amazing Pool Experience" --post_status=publish --post_content="We were skeptical about a portable pool, but EZ Pools exceeded all our expectations. The assembly was incredibly easy - my husband and I had it up and running in 45 minutes. Our kids love it, and we use it every day for exercise. Best investment we have made for our backyard! - Sarah Johnson, Florida" 2>/dev/null; then
        echo "✅ Created testimonial 1"
    else
        echo "⚠️ Failed to create testimonial 1"
    fi
else
    echo "ℹ️  Testimonial 1 already exists"
fi

if ! $WP_CLI post list --post_type=testimonial --s="Perfect for Our Training Facility" --format=count 2>/dev/null | grep -q "1"; then
    if $WP_CLI post create --post_type=testimonial --post_title="Perfect for Our Training Facility" --post_status=publish --post_content="As a swim coach, I needed a pool that could be set up quickly for our training camp. EZ Pools delivered exactly what we needed. The 25-meter pool was perfect for our Olympic hopefuls, and the quality is outstanding. We have used it for three seasons now without any issues. - Coach Mike Rodriguez, California" 2>/dev/null; then
        echo "✅ Created testimonial 2"
    else
        echo "⚠️ Failed to create testimonial 2"
    fi
else
    echo "ℹ️  Testimonial 2 already exists"
fi

# Create navigation menu
echo "🧭 Setting up navigation menu..."
MENU_ID=$($WP_CLI menu create "Primary Menu" --porcelain 2>/dev/null || $WP_CLI menu list --fields=term_id,name --format=csv 2>/dev/null | grep "Primary Menu" | cut -d, -f1 || echo "")

if [ ! -z "$MENU_ID" ]; then
    # Add menu items
    $WP_CLI menu item add-custom $MENU_ID "Home" "http://localhost:8080/" --porcelain 2>/dev/null || true
    $WP_CLI menu item add-custom $MENU_ID "Products" "http://localhost:8080/#products" --porcelain 2>/dev/null || true

    # Get page IDs safely
    ABOUT_ID=$($WP_CLI post list --post_type=page --name=about --field=ID --format=csv 2>/dev/null || echo "")
    CONTACT_ID=$($WP_CLI post list --post_type=page --name=contact --field=ID --format=csv 2>/dev/null || echo "")

    if [ ! -z "$ABOUT_ID" ]; then
        $WP_CLI menu item add-post $MENU_ID $ABOUT_ID --porcelain 2>/dev/null || true
    fi
    if [ ! -z "$CONTACT_ID" ]; then
        $WP_CLI menu item add-post $MENU_ID $CONTACT_ID --porcelain 2>/dev/null || true
    fi

    # Assign menu to location
    if $WP_CLI menu location assign $MENU_ID primary 2>/dev/null; then
        echo "✅ Created and assigned primary menu"
    else
        echo "⚠️ Created menu but failed to assign location"
    fi
else
    echo "⚠️ Could not create menu"
fi

# Set theme customizer options
echo "🎨 Setting theme customizer options..."
if $WP_CLI option update theme_mods_ezpools '{"hero_title":"The Better Portable Pool","hero_subtitle":"Custom portable pools made-to-order. From carton to completion in less than one hour. All the features of traditional pools at 1\/10th the cost.","contact_phone":"(855) 4EZ-POOL","contact_email":"<EMAIL>"}' --format=json 2>/dev/null; then
    echo "✅ Theme customizer options set"
else
    echo "⚠️ Failed to set theme customizer options"
fi

# Set site title and tagline
if $WP_CLI option update blogname "EZ Pools - The Better Portable Pool" 2>/dev/null && $WP_CLI option update blogdescription "Custom portable pools made-to-order" 2>/dev/null; then
    echo "✅ Site title and tagline set"
else
    echo "⚠️ Failed to set site title and tagline"
fi

echo "✅ EZ Pools auto-setup completed successfully!"
echo ""
echo "🎉 Your EZ Pools website is ready with:"
echo "   - EZ Pools theme activated"
echo "   - Sample pool products"
echo "   - Customer testimonials"
echo "   - Navigation menu"
echo "   - Contact information"
echo "   - About page content"
echo ""
