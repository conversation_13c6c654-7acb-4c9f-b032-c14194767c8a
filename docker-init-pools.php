<?php
/**
 * Docker Initialization Script for Pool Products
 * This script runs during Docker container startup to initialize pool products
 */

// WordPress Bootstrap
define('WP_USE_THEMES', false);
require_once('/var/www/html/wp-load.php');

// Include the migration script
require_once('/var/www/html/wp-content/themes/ezpools/pool-data-migration.php');

echo "=== EZ Pools Docker Initialization ===\n";
echo "Starting pool product initialization...\n";

try {
    // Ensure post types are registered
    do_action('init');

    // Verify post type exists
    if (!post_type_exists('pool_product')) {
        echo "ERROR: pool_product post type not registered!\n";
        exit(1);
    }
    echo "✓ pool_product post type is registered\n";

    // Check image directory
    $upload_dir = wp_upload_dir();
    $image_base_path = $upload_dir['basedir'] . '/pool-products/';
    echo "Image base path: $image_base_path\n";

    if (!is_dir($image_base_path)) {
        echo "ERROR: Image directory does not exist: $image_base_path\n";
        exit(1);
    }
    echo "✓ Image directory exists\n";

    // Check if we already have the full set of pools (30+)
    $existing_pools = get_posts(array(
        'post_type' => 'pool_product',
        'posts_per_page' => -1,
        'post_status' => 'publish'
    ));

    $pool_count = count($existing_pools);
    echo "Found $pool_count existing pool products.\n";

    if ($pool_count >= 30) {
        echo "Full set of pool products already exists. Skipping initialization.\n";
    } else {
        if ($pool_count > 0) {
            echo "Found $pool_count sample pools. Will add the remaining pools.\n";
        }
        echo "No existing pool products found. Running migration...\n";

        // Run the migration
        $migration = new EZPoolDataMigration();
        $results = $migration->run_migration();

        echo "Migration completed!\n";
        echo "Results:\n";
        echo "- Pools created: {$results['pools_created']}\n";
        echo "- Categories created: {$results['categories_created']}\n";

        if (!empty($results['errors'])) {
            echo "Errors encountered:\n";
            foreach ($results['errors'] as $error) {
                echo "  - $error\n";
            }
        }
    }
    
    echo "=== Pool Initialization Complete ===\n";
    
} catch (Exception $e) {
    echo "ERROR: Pool initialization failed: " . $e->getMessage() . "\n";
    exit(1);
}
?>
