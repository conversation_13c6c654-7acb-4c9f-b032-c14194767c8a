/**
 * Product filtering and search functionality
 */

document.addEventListener('DOMContentLoaded', function() {
    // Initialize product filtering
    initProductFilters();
    
    // Initialize product animations
    initProductAnimations();
    
    // Initialize product image galleries
    initProductGalleries();
});

/**
 * Initialize product filtering functionality
 */
function initProductFilters() {
    const categoryFilter = document.getElementById('category-filter');
    const sizeFilter = document.getElementById('size-filter');
    const priceFilter = document.getElementById('price-filter');
    const clearFilters = document.getElementById('clear-filters');
    const productCards = document.querySelectorAll('.product-card');
    
    if (!categoryFilter || !productCards.length) {
        return; // Not on products page
    }
    
    /**
     * Filter products based on current filter values
     */
    function filterProducts() {
        const categoryValue = categoryFilter.value;
        const sizeValue = sizeFilter.value;
        const priceValue = priceFilter.value;
        
        let visibleCount = 0;
        
        productCards.forEach(card => {
            const cardCategory = card.dataset.category;
            const cardSize = card.dataset.size;
            const cardPrice = card.dataset.price;
            
            const categoryMatch = !categoryValue || cardCategory === categoryValue;
            const sizeMatch = !sizeValue || cardSize === sizeValue;
            const priceMatch = !priceValue || cardPrice === priceValue;
            
            if (categoryMatch && sizeMatch && priceMatch) {
                card.style.display = 'block';
                card.style.animation = 'fadeIn 0.3s ease-in';
                visibleCount++;
            } else {
                card.style.display = 'none';
            }
        });
        
        // Show/hide no results message
        updateNoResultsMessage(visibleCount);
        
        // Update URL with current filters (for bookmarking)
        updateURL(categoryValue, sizeValue, priceValue);
    }
    
    /**
     * Update no results message
     */
    function updateNoResultsMessage(visibleCount) {
        let noResultsMsg = document.querySelector('.no-results-message');
        
        if (visibleCount === 0) {
            if (!noResultsMsg) {
                noResultsMsg = document.createElement('div');
                noResultsMsg.className = 'no-results-message';
                noResultsMsg.innerHTML = `
                    <div class="no-products">
                        <h3>No products found</h3>
                        <p>Try adjusting your filters to see more products.</p>
                        <button class="btn btn-primary" onclick="clearAllFilters()">Clear All Filters</button>
                    </div>
                `;
                document.getElementById('products-grid').appendChild(noResultsMsg);
            }
            noResultsMsg.style.display = 'block';
        } else if (noResultsMsg) {
            noResultsMsg.style.display = 'none';
        }
    }
    
    /**
     * Update URL with current filter values
     */
    function updateURL(category, size, price) {
        const url = new URL(window.location);
        
        if (category) url.searchParams.set('category', category);
        else url.searchParams.delete('category');
        
        if (size) url.searchParams.set('size', size);
        else url.searchParams.delete('size');
        
        if (price) url.searchParams.set('price', price);
        else url.searchParams.delete('price');
        
        window.history.replaceState({}, '', url);
    }
    
    /**
     * Load filters from URL parameters
     */
    function loadFiltersFromURL() {
        const url = new URL(window.location);
        const category = url.searchParams.get('category');
        const size = url.searchParams.get('size');
        const price = url.searchParams.get('price');
        
        if (category) categoryFilter.value = category;
        if (size) sizeFilter.value = size;
        if (price) priceFilter.value = price;
        
        filterProducts();
    }
    
    /**
     * Clear all filters
     */
    function clearAllFilters() {
        categoryFilter.value = '';
        sizeFilter.value = '';
        priceFilter.value = '';
        filterProducts();
    }
    
    // Make clearAllFilters globally available
    window.clearAllFilters = clearAllFilters;
    
    // Event listeners
    categoryFilter.addEventListener('change', filterProducts);
    sizeFilter.addEventListener('change', filterProducts);
    priceFilter.addEventListener('change', filterProducts);
    
    if (clearFilters) {
        clearFilters.addEventListener('click', clearAllFilters);
    }
    
    // Load initial filters from URL
    loadFiltersFromURL();
}

/**
 * Initialize product animations
 */
function initProductAnimations() {
    // Animate product cards on scroll
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };
    
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate-in');
            }
        });
    }, observerOptions);
    
    // Observe all product cards
    document.querySelectorAll('.product-card').forEach(card => {
        observer.observe(card);
    });
    
    // Observe info cards
    document.querySelectorAll('.info-card').forEach(card => {
        observer.observe(card);
    });
}

/**
 * Initialize product image galleries
 */
function initProductGalleries() {
    const mainImage = document.querySelector('.main-product-image');
    const thumbnails = document.querySelectorAll('.gallery-thumbnail');
    
    if (!mainImage || !thumbnails.length) {
        return; // Not on single product page or no gallery
    }
    
    thumbnails.forEach(thumbnail => {
        thumbnail.addEventListener('click', function(e) {
            e.preventDefault();
            
            // Update main image
            const newSrc = this.dataset.fullsize || this.src;
            const newAlt = this.alt;
            
            mainImage.src = newSrc;
            mainImage.alt = newAlt;
            
            // Update active thumbnail
            thumbnails.forEach(thumb => thumb.classList.remove('active'));
            this.classList.add('active');
            
            // Add loading animation
            mainImage.style.opacity = '0.5';
            setTimeout(() => {
                mainImage.style.opacity = '1';
            }, 200);
        });
    });
}

/**
 * Product search functionality
 */
function initProductSearch() {
    const searchInput = document.getElementById('product-search');
    const productCards = document.querySelectorAll('.product-card');
    
    if (!searchInput) return;
    
    let searchTimeout;
    
    searchInput.addEventListener('input', function() {
        clearTimeout(searchTimeout);
        
        searchTimeout = setTimeout(() => {
            const searchTerm = this.value.toLowerCase().trim();
            
            productCards.forEach(card => {
                const title = card.querySelector('.product-title').textContent.toLowerCase();
                const excerpt = card.querySelector('.product-excerpt')?.textContent.toLowerCase() || '';
                
                const matches = title.includes(searchTerm) || excerpt.includes(searchTerm);
                
                if (matches || searchTerm === '') {
                    card.style.display = 'block';
                    card.style.animation = 'fadeIn 0.3s ease-in';
                } else {
                    card.style.display = 'none';
                }
            });
        }, 300);
    });
}

/**
 * Product comparison functionality
 */
function initProductComparison() {
    const compareButtons = document.querySelectorAll('.compare-product');
    const compareList = [];
    const maxCompare = 3;
    
    compareButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            
            const productId = this.dataset.productId;
            const productTitle = this.dataset.productTitle;
            
            if (compareList.includes(productId)) {
                // Remove from comparison
                const index = compareList.indexOf(productId);
                compareList.splice(index, 1);
                this.textContent = 'Compare';
                this.classList.remove('active');
            } else if (compareList.length < maxCompare) {
                // Add to comparison
                compareList.push(productId);
                this.textContent = 'Remove from Compare';
                this.classList.add('active');
            } else {
                alert(`You can only compare up to ${maxCompare} products at once.`);
            }
            
            updateCompareWidget();
        });
    });
    
    function updateCompareWidget() {
        let widget = document.querySelector('.compare-widget');
        
        if (compareList.length === 0) {
            if (widget) widget.style.display = 'none';
            return;
        }
        
        if (!widget) {
            widget = document.createElement('div');
            widget.className = 'compare-widget';
            widget.innerHTML = `
                <div class="compare-content">
                    <h4>Compare Products (<span class="compare-count">0</span>)</h4>
                    <div class="compare-items"></div>
                    <button class="btn btn-primary compare-now">Compare Now</button>
                    <button class="btn btn-secondary clear-compare">Clear All</button>
                </div>
            `;
            document.body.appendChild(widget);
            
            // Event listeners for widget buttons
            widget.querySelector('.compare-now').addEventListener('click', () => {
                window.location.href = `/compare?products=${compareList.join(',')}`;
            });
            
            widget.querySelector('.clear-compare').addEventListener('click', () => {
                compareList.length = 0;
                compareButtons.forEach(btn => {
                    btn.textContent = 'Compare';
                    btn.classList.remove('active');
                });
                updateCompareWidget();
            });
        }
        
        widget.style.display = 'block';
        widget.querySelector('.compare-count').textContent = compareList.length;
        
        // Update items list
        const itemsContainer = widget.querySelector('.compare-items');
        itemsContainer.innerHTML = compareList.map(id => {
            const button = document.querySelector(`[data-product-id="${id}"]`);
            const title = button?.dataset.productTitle || `Product ${id}`;
            return `<div class="compare-item">${title}</div>`;
        }).join('');
    }
}

/**
 * Initialize all product functionality
 */
function initProducts() {
    initProductFilters();
    initProductAnimations();
    initProductGalleries();
    initProductSearch();
    initProductComparison();
}

// Auto-initialize when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initProducts);
} else {
    initProducts();
}
