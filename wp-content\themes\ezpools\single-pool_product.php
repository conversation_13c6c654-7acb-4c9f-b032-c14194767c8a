<?php
/**
 * Single pool product template
 */

get_header(); ?>

<?php while (have_posts()) : the_post(); 
    $width = get_post_meta(get_the_ID(), '_pool_width', true);
    $length_min = get_post_meta(get_the_ID(), '_pool_length_min', true);
    $length_max = get_post_meta(get_the_ID(), '_pool_length_max', true);
    $depth = get_post_meta(get_the_ID(), '_pool_depth', true);
    $base_price = get_post_meta(get_the_ID(), '_pool_base_price', true);
    $shipping_cost = get_post_meta(get_the_ID(), '_pool_shipping_cost', true);
    $gallons = get_post_meta(get_the_ID(), '_pool_gallons', true);
    $footprint = get_post_meta(get_the_ID(), '_pool_footprint', true);
    $top_dimensions = get_post_meta(get_the_ID(), '_pool_top_dimensions', true);
    $price_range = get_post_meta(get_the_ID(), '_pool_price_range', true);
?>

<div class="single-product-hero">
    <div class="hero-background">
        <img src="<?php echo get_template_directory_uri(); ?>/images/pool-water-ripples.jpg" alt="Pool Water" class="hero-bg-image">
        <div class="hero-overlay"></div>
        <div class="water-particles"></div>
    </div>
    <div class="hero-content">
        <div class="container">
            <nav class="breadcrumb">
                <a href="<?php echo home_url(); ?>">Home</a>
                <span class="separator">/</span>
                <a href="<?php echo get_post_type_archive_link('pool_product'); ?>">Products</a>
                <span class="separator">/</span>
                <span class="current"><?php the_title(); ?></span>
            </nav>
            <h1 class="product-title"><?php the_title(); ?></h1>
            <p class="product-subtitle">Premium Made-to-Order Pool - Built in the USA</p>
        </div>
    </div>
</div>

<div class="single-product-content">
    <div class="container">
        <div class="product-layout">
            <!-- Product Images -->
            <div class="product-images">
                <div class="main-image">
                    <?php if (has_post_thumbnail()) : ?>
                        <?php the_post_thumbnail('large', array('class' => 'main-product-image')); ?>
                    <?php else : ?>
                        <img src="<?php echo get_template_directory_uri(); ?>/images/pool-water-ripples.jpg" alt="<?php the_title(); ?>" class="main-product-image">
                    <?php endif; ?>
                </div>
                
                <!-- Gallery thumbnails would go here -->
                <div class="image-gallery">
                    <!-- Additional product images can be added here -->
                </div>
            </div>
            
            <!-- Product Details -->
            <div class="product-details">
                <div class="product-summary">
                    <div class="product-pricing">
                        <?php if ($price_range) : ?>
                            <div class="price-range"><?php echo esc_html($price_range); ?></div>
                        <?php elseif ($base_price) : ?>
                            <div class="base-price">Starting at $<?php echo number_format($base_price); ?></div>
                            <?php if ($shipping_cost) : ?>
                                <div class="shipping-cost">+ $<?php echo number_format($shipping_cost); ?> shipping</div>
                            <?php endif; ?>
                        <?php endif; ?>
                    </div>
                    
                    <div class="product-specs">
                        <h3>Specifications</h3>
                        <div class="specs-grid">
                            <?php if ($width && $length_min) : ?>
                                <div class="spec-item">
                                    <span class="spec-label">Pool Size:</span>
                                    <span class="spec-value">
                                        <?php echo esc_html($width); ?>' x <?php echo esc_html($length_min); ?>'
                                        <?php if ($length_max && $length_max != $length_min) : ?>
                                            - <?php echo esc_html($length_max); ?>'
                                        <?php endif; ?>
                                    </span>
                                </div>
                            <?php endif; ?>
                            
                            <?php if ($depth) : ?>
                                <div class="spec-item">
                                    <span class="spec-label">Depth:</span>
                                    <span class="spec-value"><?php echo esc_html($depth); ?></span>
                                </div>
                            <?php endif; ?>
                            
                            <?php if ($gallons) : ?>
                                <div class="spec-item">
                                    <span class="spec-label">Capacity:</span>
                                    <span class="spec-value"><?php echo number_format($gallons); ?> gallons</span>
                                </div>
                            <?php endif; ?>
                            
                            <?php if ($footprint) : ?>
                                <div class="spec-item">
                                    <span class="spec-label">Footprint:</span>
                                    <span class="spec-value"><?php echo esc_html($footprint); ?></span>
                                </div>
                            <?php endif; ?>
                            
                            <?php if ($top_dimensions) : ?>
                                <div class="spec-item">
                                    <span class="spec-label">Top Dimensions:</span>
                                    <span class="spec-value"><?php echo esc_html($top_dimensions); ?></span>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                    
                    <div class="product-features">
                        <h3>Features</h3>
                        <ul class="features-list">
                            <li><i class="fas fa-check"></i> Made-to-Order in the USA</li>
                            <li><i class="fas fa-check"></i> 5-Year Warranty</li>
                            <li><i class="fas fa-check"></i> American-Made Components</li>
                            <li><i class="fas fa-check"></i> Includes Flo-Kit Set</li>
                            <li><i class="fas fa-check"></i> Ships in 10-14 Business Days</li>
                            <li><i class="fas fa-check"></i> Customizable Colors Available</li>
                        </ul>
                    </div>
                    
                    <div class="product-actions">
                        <a href="#contact-form" class="btn btn-primary btn-large">Get Quote</a>
                        <a href="tel:************" class="btn btn-secondary btn-large">Call ************</a>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Product Description -->
        <div class="product-description">
            <div class="description-content">
                <h2>Product Description</h2>
                <?php the_content(); ?>
                
                <?php if (!get_the_content()) : ?>
                    <p>EZ Pools are Made-to-Order right here in the USA. They are backed by a Five-Year Warranty, made from only American-Made Components, and each pool includes one Flo-Kit Set.</p>
                    
                    <p>Our pools are designed for easy installation and long-lasting enjoyment. Whether you're looking for a lap pool for exercise or a family pool for recreation, our <?php the_title(); ?> provides the perfect solution for your backyard.</p>
                <?php endif; ?>
            </div>
        </div>
        
        <!-- Accessories Section -->
        <div class="product-accessories">
            <h2>Available Accessories</h2>
            <div class="accessories-grid">
                <div class="accessory-item">
                    <h4>A-Frame Ladder</h4>
                    <p>Add: $400.00</p>
                    <p>Essential for safe pool entry and exit.</p>
                </div>
                
                <div class="accessory-item">
                    <h4>Inside Step</h4>
                    <p>Add: $500.00</p>
                    <p>Convenient in-pool step for easy access.</p>
                </div>
                
                <div class="accessory-item">
                    <h4>Care Kit</h4>
                    <p>Add: $125.00</p>
                    <p>Everything needed for pool maintenance except sanitizer.</p>
                </div>
                
                <div class="accessory-item">
                    <h4>Solar Cover</h4>
                    <p>Add: $65.00 - $100.00</p>
                    <p>Helps keep pool clean and retains heat.</p>
                </div>
                
                <div class="accessory-item">
                    <h4>Pool Cover</h4>
                    <p>Add: $200.00 - $300.00</p>
                    <p>Heavy-duty cover for winterizing and protection.</p>
                </div>
                
                <div class="accessory-item">
                    <h4>EZ SwimMill</h4>
                    <p>Add: $50.00</p>
                    <p>Swimming treadmill for enhanced workouts.</p>
                </div>
            </div>
        </div>
        
        <!-- Options Section -->
        <div class="product-options">
            <h2>Customization Options</h2>
            <div class="options-grid">
                <div class="option-item">
                    <h4>RimGard</h4>
                    <p>Cost: $138 - $210</p>
                    <p>Cool-to-touch white material for the pool rim. Recommended for dark colored pools.</p>
                </div>
                
                <div class="option-item">
                    <h4>Swimmer's Lane Marker</h4>
                    <p>Cost: $64 - $90 each</p>
                    <p>10" wide black lane marker for the pool floor.</p>
                </div>
                
                <div class="option-item">
                    <h4>Color Change</h4>
                    <p>Cost: $250 - $450</p>
                    <p>Choose from a variety of colors for walls and floor.</p>
                </div>
                
                <div class="option-item">
                    <h4>Size Modification</h4>
                    <p>Cost: $500</p>
                    <p>Custom modifications to width, length, or depth (modifications down only).</p>
                </div>
            </div>
        </div>
        
        <!-- Contact Form -->
        <div class="product-contact" id="contact-form">
            <h2>Get Your Quote</h2>
            <div class="contact-content">
                <div class="contact-info">
                    <h3>Ready to Order?</h3>
                    <p>Contact us today for a personalized quote on your <?php the_title(); ?>. Our team will help you customize your pool and accessories to meet your specific needs.</p>
                    
                    <div class="contact-methods">
                        <div class="contact-method">
                            <i class="fas fa-phone"></i>
                            <div>
                                <strong>Call Us</strong>
                                <p>************</p>
                            </div>
                        </div>
                        
                        <div class="contact-method">
                            <i class="fas fa-envelope"></i>
                            <div>
                                <strong>Email Us</strong>
                                <p><EMAIL></p>
                            </div>
                        </div>
                        
                        <div class="contact-method">
                            <i class="fas fa-clock"></i>
                            <div>
                                <strong>Business Hours</strong>
                                <p>Mon-Fri: 9AM-5PM EST</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="contact-form">
                    <?php echo do_shortcode('[contact-form-7 id="123" title="Product Quote Form"]'); ?>
                </div>
            </div>
        </div>
        
        <!-- Related Products -->
        <div class="related-products">
            <h2>Related Products</h2>
            <div class="related-grid">
                <?php
                $related_args = array(
                    'post_type' => 'pool_product',
                    'posts_per_page' => 3,
                    'post__not_in' => array(get_the_ID()),
                    'meta_query' => array(
                        array(
                            'key' => '_pool_width',
                            'value' => $width,
                            'compare' => '='
                        )
                    )
                );
                
                $related_query = new WP_Query($related_args);
                
                if ($related_query->have_posts()) :
                    while ($related_query->have_posts()) : $related_query->the_post();
                        $related_width = get_post_meta(get_the_ID(), '_pool_width', true);
                        $related_length_min = get_post_meta(get_the_ID(), '_pool_length_min', true);
                        $related_base_price = get_post_meta(get_the_ID(), '_pool_base_price', true);
                ?>
                        <div class="related-product">
                            <a href="<?php the_permalink(); ?>">
                                <?php if (has_post_thumbnail()) : ?>
                                    <?php the_post_thumbnail('medium'); ?>
                                <?php else : ?>
                                    <img src="<?php echo get_template_directory_uri(); ?>/images/pool-water-ripples.jpg" alt="<?php the_title(); ?>">
                                <?php endif; ?>
                                <h4><?php the_title(); ?></h4>
                                <?php if ($related_width && $related_length_min) : ?>
                                    <p><?php echo esc_html($related_width); ?>' x <?php echo esc_html($related_length_min); ?>'</p>
                                <?php endif; ?>
                                <?php if ($related_base_price) : ?>
                                    <p class="price">Starting at $<?php echo number_format($related_base_price); ?></p>
                                <?php endif; ?>
                            </a>
                        </div>
                <?php
                    endwhile;
                    wp_reset_postdata();
                endif;
                ?>
            </div>
        </div>
    </div>
</div>

<?php endwhile; ?>

<?php get_footer(); ?>
