FROM wordpress:cli-php8.1

# Switch to root to install packages and configure
USER root

# Create MySQL client configuration to disable SSL in user home
RUN mkdir -p /home/<USER>
    echo '[client]' > /home/<USER>/.my.cnf && \
    echo 'ssl = false' >> /home/<USER>/.my.cnf && \
    chown -R www-data:www-data /home/<USER>

# Switch back to www-data user
USER www-data

# Set working directory
WORKDIR /var/www/html

# Default command
CMD ["wp", "--info"]
