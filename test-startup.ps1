# Test script to verify EZ Pools startup works correctly
Write-Host "🧪 Testing EZ Pools startup from scratch..." -ForegroundColor Cyan

# Stop all containers
Write-Host "🛑 Stopping all containers..." -ForegroundColor Yellow
docker-compose down

# Remove volumes to start completely fresh
Write-Host "🗑️ Removing volumes for fresh start..." -ForegroundColor Yellow
docker volume rm ezpool_db_data -f 2>$null
docker volume rm ezpool_wordpress_data -f 2>$null

# Start containers
Write-Host "🚀 Starting containers..." -ForegroundColor Green
docker-compose up -d

# Wait for setup to complete
Write-Host "⏳ Waiting for setup to complete..." -ForegroundColor Yellow
Start-Sleep 30

# Check if WordPress is accessible
Write-Host "🌐 Testing WordPress accessibility..." -ForegroundColor Green
try {
    $response = Invoke-WebRequest -Uri "http://localhost:8080" -TimeoutSec 10
    if ($response.StatusCode -eq 200) {
        Write-Host "✅ WordPress is accessible!" -ForegroundColor Green
    } else {
        Write-Host "❌ WordPress returned status code: $($response.StatusCode)" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ Failed to access WordPress: $($_.Exception.Message)" -ForegroundColor Red
}

# Test wp-cli
Write-Host "🔧 Testing wp-cli..." -ForegroundColor Green
$wpcliResult = docker-compose run --rm wp-cli wp core is-installed
if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ wp-cli is working and WordPress is installed!" -ForegroundColor Green
} else {
    Write-Host "❌ wp-cli test failed" -ForegroundColor Red
}

# Check theme
Write-Host "🎨 Checking active theme..." -ForegroundColor Green
$themeResult = docker-compose run --rm wp-cli wp theme status ezpools
if ($themeResult -match "Active") {
    Write-Host "✅ EZ Pools theme is active!" -ForegroundColor Green
} else {
    Write-Host "❌ EZ Pools theme is not active" -ForegroundColor Red
}

Write-Host "🎉 Test completed!" -ForegroundColor Cyan
Write-Host "Visit your site at: http://localhost:8080" -ForegroundColor White
Write-Host "Admin login at: http://localhost:8080/wp-admin - Username: admin, Password: admin123" -ForegroundColor White
