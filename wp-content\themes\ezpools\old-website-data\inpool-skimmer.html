<!DOCTYPE html><html lang="en-US"><head><title>EZ Pool offer the FloSkim Floating Skimmer</title><link rel="shortcut icon" href="/favicon.ico" /><meta http-equiv="Content-type" content="text/html;charset=UTF-8" /><meta name="viewport" content="width=300" /><meta name="description" content="The FloSkim floating skimmer is specifically designed for portable pools. American made and offers you as much as 6&amp;rdquo; variance with your water level. Call 855-439-7665." /><meta property="og:description" content="The FloSkim floating skimmer is specifically designed for portable pools. American made and offers you as much as 6&rdquo; variance with your water level. Call 855-439-7665." /><meta name="generator" content="EverWeb 4.0.1 (2884)" /><meta name="buildDate" content="Thursday, June 19, 2025" /><meta property="og:url" content="https://4ezpool.com/inpool-skimmer.html" /><meta property="og:title" content="EZ Pool offer the FloSkim Floating Skimmer" /><meta property="og:type" content="website" /><link rel="stylesheet" type="text/css" href="ew_css/textstyles.css?3833225815" /><link rel="stylesheet" type="text/css" href="ew_css/responsive.css?3833225815" /><link rel="stylesheet" type="text/css" href="ew_css/globaltextstyles.css?3833225815" /><script type="text/javascript" src="ew_js/imageCode.js" defer></script><script language="JavaScript">

<!--

function MM_swapImgRestore() { //v3.0

  var i,x,a=document.MM_sr; for(i=0;a&&i<a.length&&(x=a[i])&&x.oSrc;i++) x.src=x.oSrc;

}



function MM_preloadImages() { //v3.0

  var d=document; if(d.images){ if(!d.MM_p) d.MM_p=new Array();

    var i,j=d.MM_p.length,a=MM_preloadImages.arguments; for(i=0; i<a.length; i++)

    if (a[i].indexOf("#")!=0){ d.MM_p[j]=new Image; d.MM_p[j++].src=a[i];}}

}



function MM_findObj(n, d) { //v4.0

  var p,i,x;  if(!d) d=document; if((p=n.indexOf("?"))>0&&parent.frames.length) {

    d=parent.frames[n.substring(p+1)].document; n=n.substring(0,p);}

  if(!(x=d[n])&&d.all) x=d.all[n]; for (i=0;!x&&i<d.forms.length;i++) x=d.forms[i][n];

  for(i=0;!x&&d.layers&&i<d.layers.length;i++) x=MM_findObj(n,d.layers[i].document);

  if(!x && document.getElementById) x=document.getElementById(n); return x;

}



function MM_swapImage() { //v3.0

  var i,j=0,x,a=MM_swapImage.arguments; document.MM_sr=new Array; for(i=0;i<(a.length-2);i+=3)

   if ((x=MM_findObj(a[i]))!=null){document.MM_sr[j++]=x; if(!x.oSrc) x.oSrc=x.src; x.src=a[i+2];}

}


////////////////////////////////////////////////////////////////

    // make new window function

    ////////////////////////////////////////////////////////////////

    function createNewWin(newURL)

    {

        var newWindow;



        newWindow = window.open(newURL,"","scrollbars=1,resizable=1,height=400,width=400");

        

    }

	//  -->

  </script><style type="text/css">a img {border:0px;}body {background-color: #FFFEFE;margin: 0px auto;}div.container {margin: 0px auto;width: 300px;height: 408px;}</style></head><body><div class="container" style="height:408px"><div class="content" data-minheight="300"><div style="position:relative"><div class="shape_0" style="left:12px;top:289px;width:288px;height:119px;z-index:0;position: absolute;"><p class="para4"><span style="line-height: 21.74584px;" class="Style5">The all new In-Pool Skimmer from FloSkim is exclusively available for EZ Pools. Designed for inside the pool, easy to use and easy to clean. Pre-set openings are located in the corners.</span></p></div></div><div style="position:relative"><div class="shape_1" style="left:58px;top:21px;width:172px;height:152px;z-index:1;position: absolute;"><img src="images/inpool-skimmer/EZPoolSkimmer.JPG" height="152" width="172" data-src2x="images/inpool-skimmer/<EMAIL>" srcset="images/inpool-skimmer/EZPoolSkimmer.JPG 1x, images/inpool-skimmer/<EMAIL> 2x" /></div></div><div style="position:relative"><div class="shape_2" style="left:0px;top:185px;width:301px;height:89px;z-index:2;position: absolute;"><img src="images/inpool-skimmer/7x32.jpg" height="89" width="301" data-src2x="images/inpool-skimmer/<EMAIL>" srcset="images/inpool-skimmer/7x32.jpg 1x, images/inpool-skimmer/<EMAIL> 2x" /></div></div><div style="position:relative"><div class="shape_3" style="left:26.5px;top:201.5px;width:8px;height:8px;z-index:3;position: absolute;"><img src="images/inpool-skimmer/shape_3.png" height="8" width="8" style="vertical-align:top;" alt="(placeholder)" data-src2x="images/inpool-skimmer/<EMAIL>" srcset="images/inpool-skimmer/shape_3.png 1x, images/inpool-skimmer/<EMAIL> 2x" /></div></div><div style="position:relative"><div class="shape_4" style="left:267.5px;top:245.5px;width:8px;height:8px;z-index:4;position: absolute;"><img src="images/inpool-skimmer/shape_4.png" height="8" width="8" style="vertical-align:top;" alt="(placeholder)" data-src2x="images/inpool-skimmer/<EMAIL>" srcset="images/inpool-skimmer/shape_4.png 1x, images/inpool-skimmer/<EMAIL> 2x" /></div></div></div><footer data-top='408' data-height='0'></footer></div></body></html>