<!DOCTYPE html><html lang="en-US"><head><title>1HP 50 EZ Pool Pump and Filter Option</title><link rel="shortcut icon" href="/favicon.ico" /><meta http-equiv="Content-type" content="text/html;charset=UTF-8" /><meta name="viewport" content="width=300" /><meta name="description" content="The model 1050 is a 1HP 50 Sq Ft Polyester Filtering system designed or portable above-ground pools. This portable pool pump and filter plugs into a standard 110v outlet and is standard with most EZ Pools Deluxe Packages. Call 855-439-7665." /><meta property="og:description" content="The model 1050 is a 1HP 50 Sq Ft Polyester Filtering system designed or portable above-ground pools. This portable pool pump and filter plugs into a standard 110v outlet and is standard with most EZ Pools Deluxe Packages. Call 855-439-7665." /><meta name="generator" content="EverWeb 4.0.1 (2884)" /><meta name="buildDate" content="Thursday, June 19, 2025" /><meta property="og:url" content="https://4ezpool.com/1050.html" /><meta property="og:title" content="1HP 50 EZ Pool Pump and Filter Option" /><meta property="og:type" content="website" /><link rel="stylesheet" type="text/css" href="ew_css/textstyles.css?3833225815" /><link rel="stylesheet" type="text/css" href="ew_css/responsive.css?3833225815" /><link rel="stylesheet" type="text/css" href="ew_css/globaltextstyles.css?3833225815" /><script type="text/javascript" src="ew_js/imageCode.js" defer></script><script language="JavaScript">

<!--

function MM_swapImgRestore() { //v3.0

  var i,x,a=document.MM_sr; for(i=0;a&&i<a.length&&(x=a[i])&&x.oSrc;i++) x.src=x.oSrc;

}



function MM_preloadImages() { //v3.0

  var d=document; if(d.images){ if(!d.MM_p) d.MM_p=new Array();

    var i,j=d.MM_p.length,a=MM_preloadImages.arguments; for(i=0; i<a.length; i++)

    if (a[i].indexOf("#")!=0){ d.MM_p[j]=new Image; d.MM_p[j++].src=a[i];}}

}



function MM_findObj(n, d) { //v4.0

  var p,i,x;  if(!d) d=document; if((p=n.indexOf("?"))>0&&parent.frames.length) {

    d=parent.frames[n.substring(p+1)].document; n=n.substring(0,p);}

  if(!(x=d[n])&&d.all) x=d.all[n]; for (i=0;!x&&i<d.forms.length;i++) x=d.forms[i][n];

  for(i=0;!x&&d.layers&&i<d.layers.length;i++) x=MM_findObj(n,d.layers[i].document);

  if(!x && document.getElementById) x=document.getElementById(n); return x;

}



function MM_swapImage() { //v3.0

  var i,j=0,x,a=MM_swapImage.arguments; document.MM_sr=new Array; for(i=0;i<(a.length-2);i+=3)

   if ((x=MM_findObj(a[i]))!=null){document.MM_sr[j++]=x; if(!x.oSrc) x.oSrc=x.src; x.src=a[i+2];}

}


////////////////////////////////////////////////////////////////

    // make new window function

    ////////////////////////////////////////////////////////////////

    function createNewWin(newURL)

    {

        var newWindow;



        newWindow = window.open(newURL,"","scrollbars=1,resizable=1,height=400,width=400");

        

    }

	//  -->

  </script><style type="text/css">a img {border:0px;}body {background-color: #FFFEFE;margin: 0px auto;}div.container {margin: 0px auto;width: 300px;height: 501px;}</style></head><body><div class="container" style="height:501px"><div class="content" data-minheight="300"><div style="position:relative"><div class="shape_0" style="left:9px;top:284px;width:288px;height:217px;z-index:0;position: absolute;"><p class="para27"><span style="line-height: 28.14167px;" class="Style230">1HP 50 SQ FT System</span></p><p class="para4"><span style="line-height: 28.14167px;" class="Style231">The model 1050 option, available only at www.4ezpool.com, is a 1HP 50 Sq Ft Polyester Filtering system designed for portable above-ground pools. This American made pump and filter plugs into a standard 110v outlet.</span></p></div></div><div style="position:relative"><div class="shape_1" style="left:22px;top:10px;width:241px;height:287px;z-index:1;position: absolute;"><img src="images/1050/shape_pic-19.png" height="287" width="241" data-src2x="images/1050/<EMAIL>" srcset="images/1050/shape_pic-19.png 1x, images/1050/<EMAIL> 2x" /></div></div></div><footer data-top='501' data-height='0'></footer></div></body></html>