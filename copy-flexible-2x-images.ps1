# PowerShell script to copy pool images with flexible pattern matching
# This script handles various @2x image naming patterns

Write-Host "Starting flexible @2x image copy process..." -ForegroundColor Green

# Define source and destination paths
$sourceBase = "old_website\images"
$destBase = "wp-content\uploads\pool-products"

# Pool sizes to process
$poolSizes = @(
    "7x12", "7x17", "7x22", "7x27", "7x32", "7x37", "7x42", "7x47",
    "12x12", "12x17", "12x22", "12x27", "12x32", "12x37", "12x42", "12x47",
    "17x17", "17x22", "17x27", "17x32", "17x37", "17x42", "17x47",
    "22x22", "22x27", "22x32", "22x37", "22x42", "22x47", "22x52"
)

$totalImagesCopied = 0

# Copy images with flexible pattern matching
foreach ($poolSize in $poolSizes) {
    $sourceDir = Join-Path $sourceBase $poolSize
    $destDir = Join-Path $destBase $poolSize
    
    if (Test-Path $sourceDir) {
        Write-Host "Processing $poolSize..." -ForegroundColor Yellow
        
        # Create destination directory
        if (!(Test-Path $destDir)) {
            New-Item -ItemType Directory -Path $destDir -Force | Out-Null
        }
        
        # Extract width and length from pool size
        $dimensions = $poolSize.Split('x')
        $width = $dimensions[0]
        $length = $dimensions[1]
        
        # Define priority patterns to look for (in order of preference)
        $patterns = @(
            # Pattern 1: Simple number@2x (e.g., <EMAIL>, <EMAIL>)
            "$<EMAIL>",
            "$<EMAIL>",
            "$<EMAIL>", 
            "$<EMAIL>",
            
            # Pattern 2: Number with suffix (e.g., <EMAIL>, <EMAIL>)
            "$length-*@2x.png",
            "$length-*@2x.jpg",
            "$width-*@2x.png",
            "$width-*@2x.jpg",
            
            # Pattern 3: Pool size specific (e.g., <EMAIL>)
            "$poolSize*@2x.png",
            "$poolSize*@2x.jpg"
        )
        
        $imageCopied = $false
        
        # Try each pattern until we find a match
        foreach ($pattern in $patterns) {
            $matchingFiles = Get-ChildItem $sourceDir -File -Name $pattern -ErrorAction SilentlyContinue
            
            if ($matchingFiles) {
                # Take the first matching file
                $firstMatch = $matchingFiles | Select-Object -First 1
                $sourcePath = Join-Path $sourceDir $firstMatch
                $destPath = Join-Path $destDir $firstMatch
                
                Copy-Item $sourcePath $destPath -Force
                Write-Host "  Copied: $firstMatch" -ForegroundColor Cyan
                $totalImagesCopied++
                $imageCopied = $true
                break
            }
        }
        
        if (-not $imageCopied) {
            Write-Host "  No suitable @2x image found for $poolSize" -ForegroundColor Red
        }
        
        Write-Host "  Completed $poolSize" -ForegroundColor Green
    } else {
        Write-Host "  Source directory not found: $sourceDir" -ForegroundColor Red
    }
}

Write-Host "`nFlexible @2x image copy completed!" -ForegroundColor Green
Write-Host "Images copied to: $destBase" -ForegroundColor Yellow

# Display summary
$totalDirs = (Get-ChildItem $destBase -Directory).Count
Write-Host "`nSummary:" -ForegroundColor Magenta
Write-Host "- Pool directories processed: $totalDirs" -ForegroundColor Magenta
Write-Host "- Total @2x images copied: $totalImagesCopied" -ForegroundColor Magenta

# Show what was copied
Write-Host "`nImages copied:" -ForegroundColor Yellow
$copiedImages = Get-ChildItem $destBase -Recurse -File
foreach ($img in $copiedImages) {
    $poolDir = Split-Path $img.Directory.Name -Leaf
    Write-Host "  $poolDir -> $($img.Name)" -ForegroundColor Cyan
}
