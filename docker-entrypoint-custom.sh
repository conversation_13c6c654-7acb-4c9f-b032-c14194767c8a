#!/bin/bash
set -euo pipefail

# Convert Windows line endings to Unix (in case files were edited on Windows)
find /var/www/html -name "*.php" -type f -exec dos2unix {} \; 2>/dev/null || true

echo "🏊‍♂️ Starting EZ Pools WordPress Auto-Setup..."

# Wait for database to be ready
echo "⏳ Waiting for database to be ready..."
while ! mysql -h"$WORDPRESS_DB_HOST" -u"$WORDPRESS_DB_USER" -p"$WORDPRESS_DB_PASSWORD" -e "SELECT 1" >/dev/null 2>&1; do
    sleep 2
    echo "   Still waiting for database..."
done
echo "✅ Database is ready!"

# Start Apache in background
echo "🚀 Starting Apache web server..."
apache2-foreground &
APACHE_PID=$!

# Wait for Apache to be ready
echo "⏳ Waiting for Apache to start..."
while ! curl -s http://localhost > /dev/null; do
    sleep 1
done
echo "✅ Apache is ready!"

# Check if WordPress is already installed
if wp core is-installed --allow-root --path=/var/www/html 2>/dev/null; then
    echo "✅ WordPress is already installed and configured!"
else
    echo "🔧 Installing WordPress..."
    
    # Download WordPress core if not present
    if [ ! -f /var/www/html/wp-config.php ]; then
        echo "📥 Downloading WordPress core..."
        wp core download --allow-root --path=/var/www/html
    fi
    
    # Create wp-config.php if it doesn't exist
    if [ ! -f /var/www/html/wp-config.php ]; then
        echo "⚙️ Creating WordPress configuration..."
        wp config create \
            --dbname="$WORDPRESS_DB_NAME" \
            --dbuser="$WORDPRESS_DB_USER" \
            --dbpass="$WORDPRESS_DB_PASSWORD" \
            --dbhost="$WORDPRESS_DB_HOST" \
            --allow-root \
            --path=/var/www/html
    fi
    
    # Install WordPress
    echo "🔧 Installing WordPress core..."
    wp core install \
        --url="$WORDPRESS_URL" \
        --title="$WORDPRESS_TITLE" \
        --admin_user="$WORDPRESS_ADMIN_USER" \
        --admin_password="$WORDPRESS_ADMIN_PASSWORD" \
        --admin_email="$WORDPRESS_ADMIN_EMAIL" \
        --allow-root \
        --path=/var/www/html
    
    echo "✅ WordPress core installed successfully!"
    
    # Activate EZ Pools theme
    if wp theme is-installed ezpools --allow-root --path=/var/www/html; then
        echo "🎨 Activating EZ Pools theme..."
        wp theme activate ezpools --allow-root --path=/var/www/html
        echo "✅ EZ Pools theme activated!"
    else
        echo "⚠️ EZ Pools theme not found, using default theme"
    fi
    
    # Set up permalinks
    echo "🔗 Setting up pretty permalinks..."
    wp rewrite structure '/%postname%/' --allow-root --path=/var/www/html
    wp rewrite flush --allow-root --path=/var/www/html
    
    # Run the EZ Pools setup script
    echo "🏊‍♂️ Setting up EZ Pools content and configuration..."
    /usr/local/bin/auto-setup.sh

    # Initialize pool products from copied images
    echo "🏊‍♂️ Initializing pool products and images..."
    php /var/www/html/docker-init-pools.php

    echo "🎉 EZ Pools WordPress setup completed!"
fi

echo ""
echo "🌐 EZ Pools Website is ready!"
echo "================================"
echo "🏠 Website: http://localhost:8080"
echo "⚙️  Admin: http://localhost:8080/wp-admin"
echo "👤 Username: $WORDPRESS_ADMIN_USER"
echo "🔑 Password: $WORDPRESS_ADMIN_PASSWORD"
echo "🗄️  Database: http://localhost:8081"
echo ""

# Keep the container running by waiting for Apache
wait $APACHE_PID
