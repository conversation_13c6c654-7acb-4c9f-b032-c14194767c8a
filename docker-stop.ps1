# EZ Pools WordPress Docker Stop Script for Windows
# Run this script in PowerShell to stop the WordPress environment

Write-Host "🛑 Stopping EZ Pools WordPress Docker Environment" -ForegroundColor Yellow
Write-Host "=================================================" -ForegroundColor Yellow

try {
    # Check if containers are running
    $runningContainers = docker-compose ps --services --filter "status=running" 2>$null
    
    if (-not $runningContainers) {
        Write-Host "ℹ️  No containers are currently running." -ForegroundColor Blue
        exit 0
    }
    
    Write-Host "🔄 Stopping WordPress containers..." -ForegroundColor Yellow
    docker-compose down

    Write-Host "🗑️  Removing all volumes for fresh initialization..." -ForegroundColor Yellow
    docker-compose down -v

    Write-Host "🧹 Cleaning up unused Docker resources..." -ForegroundColor Yellow
    docker system prune -f --volumes

    Write-Host ""
    Write-Host "✅ EZ Pools WordPress environment stopped and cleaned!" -ForegroundColor Green
    Write-Host "🔄 All volumes removed - next start will be completely fresh" -ForegroundColor Green
    Write-Host ""
    Write-Host "📋 To start again with fresh initialization, run:" -ForegroundColor Cyan
    Write-Host "   .\docker-start.ps1" -ForegroundColor White
    Write-Host ""
    
} catch {
    Write-Host "❌ Error stopping containers: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "   Try running: docker-compose down" -ForegroundColor Yellow
}
