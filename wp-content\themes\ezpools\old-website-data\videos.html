<!DOCTYPE html><html lang="en-US"><head><title>EZ Pool Videos</title><link rel="shortcut icon" href="/favicon.ico" /><meta http-equiv="Content-type" content="text/html;charset=UTF-8" /><meta name="viewport" content="width=400" /><meta name="description" content="EZ Pools continues to add and grow helpful and demonstrative videos. EZ Pools are the very best portable pool available. Call 855-439-7665." /><meta property="og:description" content="EZ Pools continues to add and grow helpful and demonstrative videos. EZ Pools are the very best portable pool available. Call 855-439-7665." /><meta name="generator" content="EverWeb 4.0.1 (2884)" /><meta name="buildDate" content="Thursday, June 19, 2025" /><meta property="og:url" content="https://4ezpool.com/videos.html" /><meta property="og:title" content="EZ Pool Videos" /><meta property="og:type" content="website" /><link rel="stylesheet" type="text/css" href="ew_css/textstyles.css?3833225815" /><link rel="stylesheet" type="text/css" href="ew_css/responsive.css?3833225815" /><link rel="stylesheet" type="text/css" href="ew_css/globaltextstyles.css?3833225815" /><script type="text/javascript" src="ew_js/imageCode.js" defer></script><script language="JavaScript">

<!--

function MM_swapImgRestore() { //v3.0

  var i,x,a=document.MM_sr; for(i=0;a&&i<a.length&&(x=a[i])&&x.oSrc;i++) x.src=x.oSrc;

}



function MM_preloadImages() { //v3.0

  var d=document; if(d.images){ if(!d.MM_p) d.MM_p=new Array();

    var i,j=d.MM_p.length,a=MM_preloadImages.arguments; for(i=0; i<a.length; i++)

    if (a[i].indexOf("#")!=0){ d.MM_p[j]=new Image; d.MM_p[j++].src=a[i];}}

}



function MM_findObj(n, d) { //v4.0

  var p,i,x;  if(!d) d=document; if((p=n.indexOf("?"))>0&&parent.frames.length) {

    d=parent.frames[n.substring(p+1)].document; n=n.substring(0,p);}

  if(!(x=d[n])&&d.all) x=d.all[n]; for (i=0;!x&&i<d.forms.length;i++) x=d.forms[i][n];

  for(i=0;!x&&d.layers&&i<d.layers.length;i++) x=MM_findObj(n,d.layers[i].document);

  if(!x && document.getElementById) x=document.getElementById(n); return x;

}



function MM_swapImage() { //v3.0

  var i,j=0,x,a=MM_swapImage.arguments; document.MM_sr=new Array; for(i=0;i<(a.length-2);i+=3)

   if ((x=MM_findObj(a[i]))!=null){document.MM_sr[j++]=x; if(!x.oSrc) x.oSrc=x.src; x.src=a[i+2];}

}


////////////////////////////////////////////////////////////////

    // make new window function

    ////////////////////////////////////////////////////////////////

    function createNewWin(newURL)

    {

        var newWindow;



        newWindow = window.open(newURL,"","scrollbars=1,resizable=1,height=400,width=400");

        

    }

	//  -->

  </script><style type="text/css">a img {border:0px;}body {background-color: #FFFEFE;margin: 0px auto;}div.container {margin: 0px auto;width: 400px;height: 600px;}</style></head><body><div class="container" style="height:600px"><div class="content" data-minheight="600"><div style="position:relative"><div class="shape_0" style="left:0px;top:18px;width:390px;height:515px;z-index:0;position: absolute;"><div class="paraWrap" style="padding: 0px 2.16px 0px 2.16px; "><p class="para4"><span style="line-height: 15px;" class="linkStyle_196"><a href="https://www.youtube.com/watch?v=KreKk71lNNs" class="linkStyle_196">Another Happy EZ Pool Customer</a></span><span style="line-height: 15px;" class="Style195">: A very nice video testimony from a couple that bought not just one pool but two.</span></p><p class="para4"><span style="line-height: 15px;" class="linkStyle_199"><a href="https://www.youtube.com/watch?v=bLkp4L_dPxA" class="linkStyle_199">A Black Pool from EZ Pools</a></span><span style="line-height: 15px;" class="Style195">: By choosing to customize their EZ Pool and go black, the customer gets to take advantage of free sunlight for heating the pool Saves $$$.</span></p><p class="para4"><span style="line-height: 15px;" class="linkStyle_199"><a href="https://www.youtube.com/watch?v=Y3IfzWXWcik" class="linkStyle_199">Durable Event Pools from EZ Pools!</a></span><span style="line-height: 15px;" class="Style195">: This video is a 12' x 27 EZ Event Pool as part of the Gladiator Rock'nRun event held at the Rose Bowl October 1, 2011. Over 20,000 people climbed in and out of an EZ Pool as part of a 5km obstacle course!</span></p><p class="para4"><span style="line-height: 15px;" class="linkStyle_199"><a href="https://www.youtube.com/watch?v=GOU_sS-IWME" class="linkStyle_199">Knifing the Side of an EZ Pool!!!</a></span><span style="line-height: 15px;" class="Style195">: This video shows how strong the pool is,w e stab it with a knife, show water coming out, beat it with a sledgehammer, then repair it - and then beat it again after the repair. That's right. An EZ Pool is that strong!</span></p><p class="para4"><span style="line-height: 15px;" class="linkStyle_199"><a href="https://www.youtube.com/watch?v=rCvWX-_tKCw" class="linkStyle_199">See the SwimMill in Action Underwater</a></span><span style="line-height: 15px;" class="Style193">:</span><span style="line-height: 15px;" class="Style195"> Customer video demonstrates the SwimMill™ in action underwater.</span></p><p class="para4"><span style="line-height: 15px;" class="linkStyle_199"><a href="https://www.youtube.com/watch?v=PESHzfEqxCI" class="linkStyle_199">SwimMill in Action</a></span><span style="line-height: 15px;" class="Style195">: Customer demonstrates the SwimMill and provides a professional swimmer's opinion on the item.</span></p><p class="para4"><span style="line-height: 15px;" class="linkStyle_199"><a href="https://www.youtube.com/watch?v=xgkvMGXvfvo" class="linkStyle_199">Taking a Sledgehammer to the side of an EZ Pool!?</a></span><span style="line-height: 15px;" class="linkStyle_205"><a href="https://www.youtube.com/watch?v=xgkvMGXvfvo" class="linkStyle_205">!</a></span><span style="line-height: 15px;" class="Style195">: Worried about your kids in an EZ Pool? Please, we show just how tough a pool from really ez pools really is.</span></p><p class="para4"><span style="line-height: 15px;" class="linkStyle_196"><a href="https://www.youtube.com/watch?v=x9nEYfAuauA" class="linkStyle_196">Two SwimMills One Pool</a></span><span style="line-height: 15px;" class="Style195">: Customer demonstrates how they are able to use two SwimMills, at the same time, side by side, in the same pool.</span></p><p class="para4"><span style="line-height: 15px;" class="linkStyle_196"><a href="https://www.youtube.com/watch?v=dP_ePqeBavA" class="linkStyle_196">Using Vaseline to Stop a Puncture from Leaking</a></span><span style="line-height: 15px;" class="Style195">: The EZ Pool liner is so advance, if you ever get a puncture, just use a small dab of Vaseline to plug it and that's it. That's right Vaseline.</span></p></div></div></div></div><footer data-top='600' data-height='0'></footer></div></body></html>